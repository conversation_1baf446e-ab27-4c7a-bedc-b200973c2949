import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/services/shared_prefrences_service.dart';
import 'package:phoenix/features/authentication/model/credentials_model.dart';
import 'strategy_chat_select_helper.dart';

import '../utils/http_service.dart';
import 'package:phoenix/widgets/strategy_ai/typing_indicator.dart';
import 'package:phoenix/widgets/strategy_ai/chat_message_bubble.dart';
import 'package:phoenix/widgets/strategy_ai/flowchart_message_bubble.dart';
import 'package:phoenix/widgets/strategy_ai/deploy_strategy_dialog.dart';
import 'package:phoenix/widgets/strategy_ai/json_message_bubble.dart';
import 'package:flutter/services.dart';
import 'package:phoenix/widgets/strategy_ai/strategy_flowchart_view.dart';

// Helper functions for JSON highlighting
String _prettyPrintJson(String jsonString) {
  try {
    final jsonObject = jsonDecode(jsonString);
    final JsonEncoder encoder = JsonEncoder.withIndent('  ');
    return encoder.convert(jsonObject);
  } catch (e) {
    return jsonString;
  }
}

TextSpan _highlightJson(String jsonString) {
  final List<TextSpan> spans = [];
  final RegExp stringPattern = RegExp(r'"([^"\\]|\\.)*"');
  final RegExp numberPattern = RegExp(r'\b\d+\.?\d*\b');
  final RegExp booleanPattern = RegExp(r'\b(true|false)\b');
  final RegExp nullPattern = RegExp(r'\bnull\b');
  final RegExp keyPattern = RegExp(r'"([^"\\]|\\.)*"(?=\s*:)');

  int currentIndex = 0;

  while (currentIndex < jsonString.length) {
    // Find the next match
    Match? nextMatch;
    String? matchType;

    // Check for key pattern first
    final keyMatch = keyPattern.firstMatch(jsonString.substring(currentIndex));
    if (keyMatch != null) {
      nextMatch = keyMatch;
      matchType = 'key';
    } else {
      // Check other patterns
      final patterns = [
        {'pattern': stringPattern, 'type': 'string'},
        {'pattern': numberPattern, 'type': 'number'},
        {'pattern': booleanPattern, 'type': 'boolean'},
        {'pattern': nullPattern, 'type': 'null'},
      ];

      for (final patternInfo in patterns) {
        final match = (patternInfo['pattern'] as RegExp)
            .firstMatch(jsonString.substring(currentIndex));
        if (match != null &&
            (nextMatch == null || match.start < nextMatch.start)) {
          nextMatch = match;
          matchType = patternInfo['type'] as String;
        }
      }
    }

    if (nextMatch != null) {
      // Add text before the match
      if (nextMatch.start > 0) {
        spans.add(TextSpan(
          text: jsonString.substring(
              currentIndex, currentIndex + nextMatch.start),
          style: TextStyle(color: Colors.grey.withOpacity(0.9)),
        ));
      }

      // Add the highlighted match
      Color color;
      switch (matchType) {
        case 'key':
          color = const Color(0xFF569cd6); // Blue for keys
          break;
        case 'string':
          color = const Color(0xFFce9178); // Orange for strings
          break;
        case 'number':
          color = const Color(0xFFb5cea8); // Green for numbers
          break;
        case 'boolean':
          color = const Color(0xFF569cd6); // Blue for booleans
          break;
        case 'null':
          color = const Color(0xFF569cd6); // Blue for null
          break;
        default:
          color = Colors.grey.withOpacity(0.9);
      }

      spans.add(TextSpan(
        text: nextMatch.group(0),
        style: TextStyle(color: color, fontWeight: FontWeight.w500),
      ));

      currentIndex += nextMatch.start + nextMatch.group(0)!.length;
    } else {
      // Add remaining text
      spans.add(TextSpan(
        text: jsonString.substring(currentIndex),
        style: TextStyle(color: Colors.grey.withOpacity(0.9)),
      ));
      break;
    }
  }

  return TextSpan(
    children: spans,
    style: const TextStyle(
      fontFamily: 'monospace',
      fontSize: 13,
      height: 1.4,
    ),
  );
}

class StrategyChatScreen extends StatefulWidget {
  final int userId;
  const StrategyChatScreen({super.key, required this.userId});

  @override
  State<StrategyChatScreen> createState() => _StrategyChatScreenState();
}

class _StrategyChatScreenState extends State<StrategyChatScreen> {
  String? chatId;
  bool loading = true;
  bool isTyping = false;
  List<_ChatMessage> messages = [];
  String? strategyJson;
  final TextEditingController _controller = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _endOfListKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _initializeChat();
  }

  Future<void> _initializeChat() async {
    setState(() => loading = true);
    try {
      final customHttpService = HttpService();
      final response = await customHttpService.post(
        ApiPath.strategyChat(),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({}),
      );
      final data = jsonDecode(response.body);
      if (data['status'] == 'SUCCESS') {
        chatId = data['payload']['message'];
      }
    } catch (e) {
      // Handle error
    } finally {
      if (mounted) {
        setState(() => loading = false);
      }
    }
  }

  Future<void> _sendMessage(String prompt) async {
    if (chatId == null || prompt.trim().isEmpty) return;
    setState(() {
      messages.add(_ChatMessage(prompt, true));
      isTyping = true;
    });
    _controller.clear();
    await Future.delayed(Duration(milliseconds: 100));
    _scrollController.animateTo(
      _scrollController.position.maxScrollExtent + 60,
      duration: Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
    try {
      final customHttpService = HttpService();
      final response = await customHttpService.post(
        ApiPath.strategyChat(),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'chat_id': chatId,
          'prompt': prompt,
        }),
      );
      final data = jsonDecode(response.body);
      if (data['status'] == 'SUCCESS') {
        final payload = data['payload'];
        if (payload['status'] == 'VALID' &&
            payload['message'] != null &&
            payload['message'].toString().contains('JSON=')) {
          final message = payload['message'].toString();
          final jsonString = message.substring(message.indexOf('JSON=') + 5);
          if (mounted) {
            setState(() {
              messages.add(_ChatMessage(jsonString, false, isJson: true));
              isTyping = false;
            });
          }
          await Future.delayed(Duration(milliseconds: 100));
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent + 120,
            duration: Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        } else if (payload['message'] != null) {
          if (mounted) {
            setState(() {
              messages.add(_ChatMessage(payload['message'].toString(), false));
              isTyping = false;
            });
          }
          await Future.delayed(Duration(milliseconds: 100));
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent + 60,
            duration: Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        } else {
          if (mounted) {
            setState(() {
              isTyping = false;
            });
          }
        }
      } else {
        if (mounted) {
          setState(() {
            isTyping = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isTyping = false;
        });
      }
    }
  }

  Future<void> _createNewSession() async {
    setState(() => loading = true);
    try {
      final customHttpService = HttpService();
      final response = await customHttpService.post(
        ApiPath.strategyChat(),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({}),
      );
      final data = jsonDecode(response.body);
      if (data['status'] == 'SUCCESS') {
        setState(() {
          chatId = data['payload']['message'];
          messages.clear();
        });
      }
    } catch (e) {
      // Handle error
    } finally {
      if (mounted) setState(() => loading = false);
    }
  }

  Future<void> _showChatHistory(bool isDarkMode) async {
    setState(() => loading = true);
    Map<String, dynamic>? grouped;
    try {
      final customHttpService = HttpService();
      final response = await customHttpService.get(
        ApiPath.strategyChatHistory(widget.userId),
        headers: {'Content-Type': 'application/json'},
      );
      final data = jsonDecode(response.body);
      if (data['status'] == 'SUCCESS' && data['payload'] is Map) {
        grouped = Map<String, dynamic>.from(data['payload']);
      }
    } catch (e) {
      // Handle error
    } finally {
      if (mounted) setState(() => loading = false);
    }
    if (mounted && grouped != null) {
      showModalBottomSheet(
        context: context,
        backgroundColor: AppTheme.backgroundColor(isDarkMode),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
        ),
        builder: (context) {
          return _ChatHistoryListGrouped(
            groupedHistory: grouped!,
            onSessionTap: (chatId) {
              _fetchAndShowChatMessages(chatId);
            },
          );
        },
      );
    }
  }

  // New: Fetch chat history messages for a selected chat session
  Future<void> _fetchAndShowChatMessages(String chatId) async {
    setState(() => loading = true);
    try {
      final customHttpService = HttpService();
      final response = await customHttpService.post(
        ApiPath.refreshOldStrategy(),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'chat_id': chatId,
        }),
      );
      final data = jsonDecode(response.body);
      if (data['status'] == 'SUCCESS' && data['payload'] is List) {
        final List<dynamic> chatMessages = data['payload'];
        final List<_ChatMessage> loadedMessages = chatMessages.map((msg) {
          final sender = msg['sender']?.toString() ?? '';
          final text = msg['message']?.toString() ?? '';
          final isUser = sender == 'user';
          final isJson = sender == 'bot' && text.startsWith('JSON=');
          final displayText = isJson ? text.substring(5) : text;
          return _ChatMessage(displayText, isUser, isJson: isJson);
        }).toList();
        setState(() {
          this.chatId = chatId;
          messages = loadedMessages;
        });
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_scrollController.hasClients) {
            _scrollController
                .jumpTo(_scrollController.position.maxScrollExtent);
            Future.delayed(Duration(milliseconds: 100), () {
              if (_scrollController.hasClients) {
                _scrollController.animateTo(
                  _scrollController.position.maxScrollExtent,
                  duration: Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                );
              }
            });
          }
        });
      }
    } catch (e) {
      // Optionally show error
    } finally {
      if (mounted) setState(() => loading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Scaffold(
          backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
          appBar: AppBar(
            backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
            elevation: 0,
            centerTitle: true,
            title: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.auto_awesome,
                    color: AppTheme.primaryColor(themeState.isDarkMode),
                    size: 28),
                SizedBox(width: 8),
                Text('Strategy AI Chat',
                    style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontWeight: FontWeight.bold,
                        fontSize: 20)),
              ],
            ),
            leading: IconButton(
              icon: Icon(Icons.arrow_back_ios_new_rounded,
                  color: AppTheme.textPrimary(themeState.isDarkMode)),
              onPressed: () => Navigator.of(context).pop(),
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.history,
                    color: AppTheme.textPrimary(themeState.isDarkMode)),
                onPressed: () => _showChatHistory(themeState.isDarkMode),
                tooltip: 'History',
              ),
              IconButton(
                icon: Icon(Icons.add,
                    color: AppTheme.textPrimary(themeState.isDarkMode)),
                onPressed: _createNewSession,
                tooltip: 'New Session',
              ),
            ],
          ),
          body: loading
              ? Center(child: CircularProgressIndicator())
              : Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppTheme.backgroundColor(themeState.isDarkMode),
                            AppTheme.cardColor(themeState.isDarkMode).withOpacity(0.7)
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                    ),
                    Column(
                      children: [
                        Expanded(
                          child: ListView.builder(
                            controller: _scrollController,
                            padding: EdgeInsets.only(top: 12, bottom: 12),
                            itemCount: messages.length + (isTyping ? 1 : 0) + 1,
                            itemBuilder: (context, index) {
                              if (index ==
                                  messages.length + (isTyping ? 1 : 0)) {
                                return SizedBox(key: _endOfListKey, height: 1);
                              }
                              if (isTyping && index == messages.length) {
                                return const TypingIndicator();
                              }
                              final msg = messages[index];
                              if (msg.isJson) {
                                // Show flowchart by default, with a toggle to JSON view
                                return StatefulBuilder(
                                  builder: (context, setState) {
                                    if (!msg.showJsonView) {
                                      // Show flowchart bubble with a button to switch to JSON
                                      return FlowchartMessageBubble(
                                        jsonText: msg.text,
                                        onDeploy: () {
                                          String strategyName = '';
                                          final ValueNotifier<bool> isValid =
                                              ValueNotifier<bool>(false);
                                          final TextEditingController controller =
                                              TextEditingController();
                                          final String jsonData = msg.text;
                                          showDialog(
                                            context: context,
                                            builder: (context) {
                                              return DeployStrategyDialog(
                                                controller: controller,
                                                isValid: isValid,
                                                strategyName: strategyName,
                                                onChanged: (value) {
                                                  strategyName = value;
                                                  final validPattern =
                                                      RegExp(r'^[A-Za-z0-9_]+\u0000?$');
                                                  isValid.value =
                                                      value.trim().isNotEmpty &&
                                                          validPattern
                                                              .hasMatch(value.trim());
                                                },
                                                onCancel: () =>
                                                    Navigator.of(context).pop(),
                                                onDeploy: () async {
                                                  String? errorMsg;
                                                  try {
                                                    showDialog(
                                                      context: context,
                                                      barrierDismissible: false,
                                                      builder:
                                                          (BuildContext context) {
                                                        return Dialog(
                                                          backgroundColor:
                                                              Colors.transparent,
                                                          elevation: 0,
                                                          child: Container(
                                                            padding:
                                                                EdgeInsets.all(16),
                                                            decoration: BoxDecoration(
                                                              color: ThemeConstants
                                                                  .backgroundColor,
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(16),
                                                              boxShadow: ThemeConstants
                                                                  .neomorpicShadow,
                                                            ),
                                                            child: Column(
                                                              mainAxisSize:
                                                                  MainAxisSize.min,
                                                              children: [
                                                                CircularProgressIndicator(
                                                                  color:
                                                                      ThemeConstants
                                                                          .blue,
                                                                ),
                                                                SizedBox(
                                                                    height: 16),
                                                                Text(
                                                                  'Deploying strategy...',
                                                                  style: TextStyle(
                                                                    color:
                                                                        ThemeConstants
                                                                            .zenWhite,
                                                                    fontSize: 16,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        );
                                                      },
                                                    );
                                                    final authState = context
                                                        .read<AuthBloc>()
                                                        .state;
                                                    int? clientId;
                                                    List<BrokerInfo> brokers = [];
                                                    if (authState
                                                        is AuthAuthenticated) {
                                                      clientId = authState
                                                          .credentialsModel.clientId;
                                                      brokers = authState
                                                          .credentialsModel.brokers;
                                                    }
                                                    if (clientId == null) {
                                                      throw Exception(
                                                          'No client ID found');
                                                    }
                                                    final prefs =
                                                        SharedPreferencesService
                                                            .instance;
                                                    String? defaultBrokerName = prefs
                                                        .getDefaultBroker(clientId);
                                                    String? defaultAccountIdStr =
                                                        prefs.getDefaultAccount(
                                                            clientId);
                                                    if (defaultBrokerName == null ||
                                                        defaultAccountIdStr == null) {
                                                      Navigator.of(context).pop();
                                                      final result =
                                                          await showBrokerAccountStrategySelector(
                                                              context);
                                                      if (result == null ||
                                                          result['brokerName'] ==
                                                              null ||
                                                          result['accountId'] ==
                                                              null) {
                                                        throw Exception(
                                                            'Broker/account selection cancelled.');
                                                      }
                                                      defaultBrokerName =
                                                          result['brokerName'];
                                                      defaultAccountIdStr =
                                                          result['accountId']
                                                              .toString();
                                                      showDialog(
                                                        context: context,
                                                        barrierDismissible: false,
                                                        builder:
                                                            (BuildContext context) {
                                                          return Dialog(
                                                            backgroundColor:
                                                                Colors.transparent,
                                                            elevation: 0,
                                                            child: Container(
                                                              padding:
                                                                  EdgeInsets.all(16),
                                                              decoration: BoxDecoration(
                                                                color: ThemeConstants
                                                                    .backgroundColor,
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(16),
                                                                boxShadow: ThemeConstants
                                                                    .neomorpicShadow,
                                                              ),
                                                              child: Column(
                                                                mainAxisSize:
                                                                    MainAxisSize.min,
                                                                children: [
                                                                  CircularProgressIndicator(
                                                                    color:
                                                                        ThemeConstants
                                                                            .blue,
                                                                  ),
                                                                  SizedBox(
                                                                      height: 16),
                                                                  Text(
                                                                    'Deploying strategy...',
                                                                    style: TextStyle(
                                                                      color:
                                                                          ThemeConstants
                                                                              .zenWhite,
                                                                      fontSize: 16,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          );
                                                        },
                                                      );
                                                    }
                                                    final broker = brokers.firstWhere(
                                                      (b) =>
                                                          b.brokerName ==
                                                          defaultBrokerName,
                                                      orElse: () => throw Exception(
                                                          'Default broker not found in credentials'),
                                                    );
                                                    final account =
                                                        broker.accounts.firstWhere(
                                                      (a) =>
                                                          a.accountId.toString() ==
                                                          defaultAccountIdStr,
                                                      orElse: () => throw Exception(
                                                          'Default account not found in broker'),
                                                    );
                                                    final customHttpService =
                                                        HttpService();
                                                    final response =
                                                        await customHttpService.post(
                                                      ApiPath.strategySave(),
                                                      headers: {
                                                        'Content-Type':
                                                            'application/json'
                                                      },
                                                      body: jsonEncode({
                                                        'client_id': clientId,
                                                        'account_id':
                                                            account.accountId,
                                                        'broker_id': broker.brokerId,
                                                        'strategy_name': strategyName,
                                                        'json': jsonData
                                                      }),
                                                    );
                                                    Navigator.of(context).pop();
                                                    Navigator.of(context).pop();
                                                    final data =
                                                        jsonDecode(response.body);
                                                    if (data['status'] == 'SUCCESS') {
                                                      ScaffoldMessenger.of(context)
                                                          .showSnackBar(
                                                        SnackBar(
                                                          content: Text(
                                                              'Strategy "$strategyName" deployed successfully!'),
                                                          backgroundColor:
                                                              ThemeConstants
                                                                  .toastSuccessColor,
                                                        ),
                                                      );
                                                    } else {
                                                      errorMsg =
                                                          'Failed to deploy strategy: \\${data['message'] ?? 'Unknown error'}';
                                                      ScaffoldMessenger.of(context)
                                                          .showSnackBar(
                                                        SnackBar(
                                                          content: Text(errorMsg),
                                                          backgroundColor:
                                                              ThemeConstants
                                                                  .toastFailedColor,
                                                        ),
                                                      );
                                                    }
                                                  } catch (e) {
                                                    Navigator.of(context).pop();
                                                    Navigator.of(context).pop();
                                                    errorMsg =
                                                        'Error deploying strategy: \\${e.toString()}';
                                                    ScaffoldMessenger.of(context)
                                                        .showSnackBar(
                                                      SnackBar(
                                                        content: Text(errorMsg),
                                                        backgroundColor:
                                                            ThemeConstants
                                                                .toastFailedColor,
                                                      ),
                                                    );
                                                  }
                                                },
                                              );
                                            },
                                          );
                                        },
                                        highlightJson: _highlightJson,
                                        prettyPrintJson: _prettyPrintJson,
                                        // Add a button to switch to JSON view
                                        // We'll add a callback to setState and update msg.showJsonView
                                        // The FlowchartMessageBubble already has a 'View JSON' button, so we override its onPressed
                                        // We'll pass a callback to FlowchartMessageBubble for this
                                        // But since it's not in the original props, we can wrap the IconButton in FlowchartMessageBubble with a callback
                                        // For now, we can use a closure here:
                                        // We'll need to update FlowchartMessageBubble to accept an onViewJson callback
                                        // So, let's assume we add that prop:
                                        onViewJson: () {
                                          setState(() {
                                            msg.showJsonView = true;
                                          });
                                        },
                                      );
                                    } else {
                                      // Show JSON bubble with a button to switch to flowchart
                                      return JsonMessageBubble(
                                        jsonText: msg.text,
                                        highlightJson: _highlightJson,
                                        prettyPrintJson: _prettyPrintJson,
                                        onCopy: () {
                                          Clipboard.setData(
                                              ClipboardData(text: msg.text));
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            const SnackBar(
                                                content: Text(
                                                    'JSON copied to clipboard!')),
                                          );
                                        },
                                        onViewChart: () {
                                          setState(() {
                                            msg.showJsonView = false;
                                          });
                                        },
                                        onDeploy: () {
                                          String strategyName = '';
                                          final ValueNotifier<bool> isValid =
                                              ValueNotifier<bool>(false);
                                          final TextEditingController controller =
                                              TextEditingController();
                                          final String jsonData = msg.text;
                                          showDialog(
                                            context: context,
                                            builder: (context) {
                                              return DeployStrategyDialog(
                                                controller: controller,
                                                isValid: isValid,
                                                strategyName: strategyName,
                                                onChanged: (value) {
                                                  strategyName = value;
                                                  final validPattern =
                                                      RegExp(r'^[A-Za-z0-9_]+\u0000?$');
                                                  isValid.value =
                                                      value.trim().isNotEmpty &&
                                                          validPattern
                                                              .hasMatch(value.trim());
                                                },
                                                onCancel: () =>
                                                    Navigator.of(context).pop(),
                                                onDeploy: () async {
                                                  String? errorMsg;
                                                  try {
                                                    showDialog(
                                                      context: context,
                                                      barrierDismissible: false,
                                                      builder:
                                                          (BuildContext context) {
                                                        return Dialog(
                                                          backgroundColor:
                                                              Colors.transparent,
                                                          elevation: 0,
                                                          child: Container(
                                                            padding:
                                                                EdgeInsets.all(16),
                                                            decoration: BoxDecoration(
                                                              color: ThemeConstants
                                                                  .backgroundColor,
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(16),
                                                              boxShadow: ThemeConstants
                                                                  .neomorpicShadow,
                                                            ),
                                                            child: Column(
                                                              mainAxisSize:
                                                                  MainAxisSize.min,
                                                              children: [
                                                                CircularProgressIndicator(
                                                                  color:
                                                                      ThemeConstants
                                                                          .blue,
                                                                ),
                                                                SizedBox(
                                                                    height: 16),
                                                                Text(
                                                                  'Deploying strategy...',
                                                                  style: TextStyle(
                                                                    color:
                                                                        ThemeConstants
                                                                            .zenWhite,
                                                                    fontSize: 16,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        );
                                                      },
                                                    );
                                                    final authState = context
                                                        .read<AuthBloc>()
                                                        .state;
                                                    int? clientId;
                                                    List<BrokerInfo> brokers = [];
                                                    if (authState
                                                        is AuthAuthenticated) {
                                                      clientId = authState
                                                          .credentialsModel.clientId;
                                                      brokers = authState
                                                          .credentialsModel.brokers;
                                                    }
                                                    if (clientId == null) {
                                                      throw Exception(
                                                          'No client ID found');
                                                    }
                                                    final prefs =
                                                        SharedPreferencesService
                                                            .instance;
                                                    String? defaultBrokerName = prefs
                                                        .getDefaultBroker(clientId);
                                                    String? defaultAccountIdStr =
                                                        prefs.getDefaultAccount(
                                                            clientId);
                                                    if (defaultBrokerName == null ||
                                                        defaultAccountIdStr == null) {
                                                      Navigator.of(context).pop();
                                                      final result =
                                                          await showBrokerAccountStrategySelector(
                                                              context);
                                                      if (result == null ||
                                                          result['brokerName'] ==
                                                              null ||
                                                          result['accountId'] ==
                                                              null) {
                                                        throw Exception(
                                                            'Broker/account selection cancelled.');
                                                      }
                                                      defaultBrokerName =
                                                          result['brokerName'];
                                                      defaultAccountIdStr =
                                                          result['accountId']
                                                              .toString();
                                                      showDialog(
                                                        context: context,
                                                        barrierDismissible: false,
                                                        builder:
                                                            (BuildContext context) {
                                                          return Dialog(
                                                            backgroundColor:
                                                                Colors.transparent,
                                                            elevation: 0,
                                                            child: Container(
                                                              padding:
                                                                  EdgeInsets.all(16),
                                                              decoration: BoxDecoration(
                                                                color: ThemeConstants
                                                                    .backgroundColor,
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(16),
                                                                boxShadow: ThemeConstants
                                                                    .neomorpicShadow,
                                                              ),
                                                              child: Column(
                                                                mainAxisSize:
                                                                    MainAxisSize.min,
                                                                children: [
                                                                  CircularProgressIndicator(
                                                                    color:
                                                                        ThemeConstants
                                                                            .blue,
                                                                  ),
                                                                  SizedBox(
                                                                      height: 16),
                                                                  Text(
                                                                    'Deploying strategy...',
                                                                    style: TextStyle(
                                                                      color:
                                                                          ThemeConstants
                                                                              .zenWhite,
                                                                      fontSize: 16,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          );
                                                        },
                                                      );
                                                    }
                                                    final broker = brokers.firstWhere(
                                                      (b) =>
                                                          b.brokerName ==
                                                          defaultBrokerName,
                                                      orElse: () => throw Exception(
                                                          'Default broker not found in credentials'),
                                                    );
                                                    final account =
                                                        broker.accounts.firstWhere(
                                                      (a) =>
                                                          a.accountId.toString() ==
                                                          defaultAccountIdStr,
                                                      orElse: () => throw Exception(
                                                          'Default account not found in broker'),
                                                    );
                                                    final customHttpService =
                                                        HttpService();
                                                    final response =
                                                        await customHttpService.post(
                                                      ApiPath.strategySave(),
                                                      headers: {
                                                        'Content-Type':
                                                            'application/json'
                                                      },
                                                      body: jsonEncode({
                                                        'client_id': clientId,
                                                        'account_id':
                                                            account.accountId,
                                                        'broker_id': broker.brokerId,
                                                        'strategy_name': strategyName,
                                                        'json': jsonData
                                                      }),
                                                    );
                                                    Navigator.of(context).pop();
                                                    Navigator.of(context).pop();
                                                    final data =
                                                        jsonDecode(response.body);
                                                    if (data['status'] == 'SUCCESS') {
                                                      ScaffoldMessenger.of(context)
                                                          .showSnackBar(
                                                        SnackBar(
                                                          content: Text(
                                                              'Strategy "$strategyName" deployed successfully!'),
                                                          backgroundColor:
                                                              ThemeConstants
                                                                  .toastSuccessColor,
                                                        ),
                                                      );
                                                    } else {
                                                      errorMsg =
                                                          'Failed to deploy strategy: \\${data['message'] ?? 'Unknown error'}';
                                                      ScaffoldMessenger.of(context)
                                                          .showSnackBar(
                                                        SnackBar(
                                                          content: Text(errorMsg),
                                                          backgroundColor:
                                                              ThemeConstants
                                                                  .toastFailedColor,
                                                        ),
                                                      );
                                                    }
                                                  } catch (e) {
                                                    Navigator.of(context).pop();
                                                    Navigator.of(context).pop();
                                                    errorMsg =
                                                        'Error deploying strategy: \\${e.toString()}';
                                                    ScaffoldMessenger.of(context)
                                                        .showSnackBar(
                                                      SnackBar(
                                                        content: Text(errorMsg),
                                                        backgroundColor:
                                                            ThemeConstants
                                                                .toastFailedColor,
                                                      ),
                                                    );
                                                  }
                                                },
                                              );
                                            },
                                          );
                                        },
                                      );
                                    }
                                  },
                                );
                              }
                              return ChatMessageBubble(
                                text: msg.text,
                                isUser: msg.isUser,
                              );
                            },
                          ),
                        ),
                        // Modern floating input bar
                        Padding(
                          padding: const EdgeInsets.fromLTRB(12, 0, 12, 18),
                          child: Material(
                            elevation: 10,
                            borderRadius: BorderRadius.circular(24),
                            color: Colors.transparent,
                            child: Container(
                              decoration: BoxDecoration(
                                color: AppTheme.cardColor(themeState.isDarkMode),
                                borderRadius: BorderRadius.circular(24),
                                border: Border.all(
                                  color: AppTheme.borderColor(themeState.isDarkMode),
                                  width: 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                      color: themeState.isDarkMode ? Colors.black26 : Colors.grey.withOpacity(0.2),
                                      blurRadius: 12,
                                      offset: Offset(0, 4))
                                ],
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: TextField(
                                      controller: _controller,
                                      onSubmitted: _sendMessage,
                                      style: TextStyle(
                                          color: AppTheme.textPrimary(themeState.isDarkMode)),
                                      decoration: InputDecoration(
                                        hintText: 'Type your message...',
                                        hintStyle: TextStyle(
                                            color: AppTheme.textSecondary(themeState.isDarkMode)),
                                        border: InputBorder.none,
                                        contentPadding: EdgeInsets.symmetric(
                                            vertical: 18, horizontal: 20),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 6),
                                  // Modern send button
                                  Container(
                                    margin: EdgeInsets.only(right: 8),
                                    child: GestureDetector(
                                      onTap: () {
                                        if (_controller.text
                                            .trim()
                                            .isNotEmpty) {
                                          _sendMessage(_controller.text.trim());
                                        }
                                      },
                                      child: AnimatedContainer(
                                        duration: Duration(milliseconds: 150),
                                        curve: Curves.easeInOut,
                                        width: 48,
                                        height: 48,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          gradient: LinearGradient(
                                            colors: [
                                              AppTheme.primaryColor(themeState.isDarkMode),
                                              AppTheme.primaryColor(themeState.isDarkMode)
                                                  .withOpacity(0.7)
                                            ],
                                            begin: Alignment.topLeft,
                                            end: Alignment.bottomRight,
                                          ),
                                          boxShadow: [
                                            BoxShadow(
                                              color: AppTheme.primaryColor(themeState.isDarkMode)
                                                  .withOpacity(0.25),
                                              blurRadius: 12,
                                              offset: Offset(0, 4),
                                            ),
                                          ],
                                        ),
                                        child: Center(
                                          child: Icon(
                                            Icons.send_rounded,
                                            size: 28,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
        );
      },
    );
  }
}

class _ChatMessage {
  final String text;
  final bool isUser;
  final bool isJson;
  // Add a field to track the current view for this message (flowchart or json)
  bool showJsonView;
  _ChatMessage(this.text, this.isUser, {this.isJson = false, this.showJsonView = false});
}

class _ChatHistoryListGrouped extends StatelessWidget {
  final Map<String, dynamic> groupedHistory;
  final void Function(String chatId) onSessionTap;
  const _ChatHistoryListGrouped(
      {required this.groupedHistory, required this.onSessionTap});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(builder: (context, themeState) {
      final List<Widget> children = [];
      groupedHistory.forEach((section, sessions) {
        if (sessions is List && sessions.isNotEmpty) {
          children.add(Padding(
            padding: const EdgeInsets.fromLTRB(8, 18, 8, 6),
            child: Text(
              section,
              style: TextStyle(
                color: AppTheme.primaryColor(themeState.isDarkMode),
                fontWeight: FontWeight.bold,
                fontSize: 16,
                letterSpacing: 0.5,
              ),
            ),
          ));
          for (final item in sessions) {
            children.add(Card(
              color: AppTheme.cardColor(themeState.isDarkMode),
              elevation: 2,
              margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 0),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              child: ListTile(
                leading:
                    Icon(Icons.chat_bubble_outline, color: AppTheme.primaryColor(themeState.isDarkMode)),
                title: Text(item['chatName'] ?? 'Session',
                    style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontWeight: FontWeight.w600)),
                subtitle: item['chatId'] != null
                    ? Text(item['chatId'],
                        style: TextStyle(
                            color: AppTheme.textSecondary(themeState.isDarkMode),
                            fontSize: 12))
                    : null,
                onTap: () {
                  Navigator.pop(context);
                  if (item['chatId'] != null) {
                    onSessionTap(item['chatId']);
                  }
                },
              ),
            ));
          }
        }
      });
      if (children.isEmpty) {
        return Padding(
          padding: const EdgeInsets.all(32.0),
          child: Center(
            child: Text('No chat history found',
                style: TextStyle(
                    color: AppTheme.textSecondary(themeState.isDarkMode),
                    fontSize: 18)),
          ),
        );
      }
      return ListView(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
        children: children,
      );
    });
  }
}
