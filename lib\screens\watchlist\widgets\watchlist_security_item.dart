import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_inset_box_shadow/flutter_inset_box_shadow.dart';
import 'package:intl/intl.dart';
import 'package:phoenix/features/portfolio_data/model/position_model.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/order_form/security_list_dropdown/security_list_text_formatter.dart';

class WatchlistSecurityItem extends StatelessWidget {
  final SecurityModel security;
  final double? price;
  final double sodPrice;
  final PositionsModel position;
  final bool isReorderable;
  final VoidCallback? onDelete;
  final Widget? deleteBackground;

  const WatchlistSecurityItem({
    super.key,
    required this.security,
    this.price,
    required this.sodPrice,
    required this.position,
    this.isReorderable = true,
    this.onDelete,
    this.deleteBackground,
  });

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(locale: 'en_IN', symbol: '₹');
    final change = price != null && sodPrice != 0 ? price! - sodPrice : 0.0;
    final percentageChange = price != null && sodPrice != 0
        ? ((price! - sodPrice) / sodPrice) * 100
        : 0.0;
    final isGain = change >= 0;
    final qty = position.position;

    Widget listTile = Container(
      padding: const EdgeInsets.symmetric(vertical: 5),
      decoration: BoxDecoration(
        color: ThemeConstants.backgroundColor,
        borderRadius: BorderRadius.circular(10),
        boxShadow: ThemeConstants.neomorpicShadow,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 15),
        title: Text(
          SecurityListTextFormatter.format(security, security.instrumentType),
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 15,
            color: Colors.white,
          ),
        ),
        subtitle: Text(
          security.tradingSymbol,
          style: const TextStyle(
            color: Color(0xffCDCDCD),
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
        ),
        trailing: _buildTrailingWidget(
          qty: qty,
          price: price,
          change: change,
          percentageChange: percentageChange,
          isGain: isGain,
          currencyFormat: currencyFormat,
          isReorderable: isReorderable,
        ),
      ),
    );

    if (onDelete != null && deleteBackground != null) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
        child: _buildDismissibleItem(listTile),
      );
    }

    return listTile;
  }

  Widget _buildDismissibleItem(Widget child) {
    return Dismissible(
      key: Key('${security.tradingSymbol}_dismissible'),
      background: deleteBackground,
      secondaryBackground: deleteBackground,
      direction: DismissDirection.endToStart,
      onDismissed: (direction) => onDelete?.call(),
      child: child,
    );
  }

  Widget _buildTrailingWidget({
    required int qty,
    required double? price,
    required double change,
    required double percentageChange,
    required bool isGain,
    required NumberFormat currencyFormat,
    required bool isReorderable,
  }) {
    return SizedBox(
      height: 100,
      width: 120,
      child: FittedBox(
        fit: BoxFit.cover,
        alignment: Alignment.centerRight,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              'Qty: $qty',
              style: TextStyle(
                fontSize: isReorderable ? 10 : 15,
                fontWeight: FontWeight.w400,
                color: isReorderable 
                    ? const Color.fromARGB(255, 122, 120, 120)
                    : Colors.white,
              ),
            ),
            Text(
              price != null ? currencyFormat.format(price) : (isReorderable ? "---" : "N/A"),
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: isReorderable ? 10 : 15,
                color: Colors.white,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                color: isGain
                    ? ThemeConstants.tileGreenColor.withOpacity(0.1)
                    : ThemeConstants.titleRedColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                '${change.toStringAsFixed(2)} (${percentageChange.toStringAsFixed(2)}%)',
                style: TextStyle(
                  color: isGain
                      ? ThemeConstants.tileGreenColor
                      : ThemeConstants.titleRedColor,
                  fontWeight: FontWeight.w400,
                  fontSize: isReorderable ? 10 : 15,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}