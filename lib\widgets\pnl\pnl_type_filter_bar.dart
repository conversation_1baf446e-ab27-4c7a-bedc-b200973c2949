import 'package:flutter/material.dart';

class PnlTypeFilterBar extends StatefulWidget {
  final Function(String) onFilterSelected;

  const PnlTypeFilterBar({
    super.key,
    required this.onFilterSelected,
  });

  @override
  State<PnlTypeFilterBar> createState() => _TypeFilterRowState();
}

class _TypeFilterRowState extends State<PnlTypeFilterBar> {
  final List<String> filters = ['LTD', 'DTD', 'WTD', 'MTD', 'YTD'];
  final Map<String,String> filterMap = {
    'LTD': 'latest',
    'DTD': 'sod',
    'WTD': 'sow',
    'MTD': 'som',
    'YTD': 'soy',
  };

  String selectedFilter = 'LTD'; // Default selected filter

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      height: 36,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: filters.map((filter) {
              final isSelected = filter == selectedFilter;
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 4.0),
                height: 26,
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      selectedFilter = filter;
                    });
                    widget.onFilterSelected(filterMap[filter]!);
                  },
                  child: Container(
                    width: 56,
                    
                    decoration: BoxDecoration(
                      color: isSelected ? const Color(0xFF3B82F6) : const Color(0xFF333333),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Center(
                      child: Text(
                        filter,
                        style: TextStyle(
                          color:  isSelected ? Colors.white : Colors.grey.shade400,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}

