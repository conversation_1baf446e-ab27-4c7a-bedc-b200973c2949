import 'package:flutter/material.dart';
import 'package:phoenix/utils/theme_constants.dart';

class OrderTimelineWidget extends StatelessWidget {
  final List<TimelineItem> timelineItems;
  final String overAllStatus;

  const OrderTimelineWidget({
    super.key,
    required this.timelineItems,
    required this.overAllStatus,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Order Timeline',
          style: TextStyle(
            color: ThemeConstants.zenWhite,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        ...timelineItems.asMap().entries.map((entry) {
          int index = entry.key;
          TimelineItem item = entry.value;
          bool isLast = index == timelineItems.length - 1;

          return TimelineItemWidget(
            item: item,
            isLast: isLast,
            isOrderCompleted: overAllStatus == 'COMPLETED',
          );
        }),
      ],
    );
  }
}

class TimelineItemWidget extends StatefulWidget {
  final TimelineItem item;
  final bool isLast;
  final int animationDelay;
  final bool isOrderCompleted;

  const TimelineItemWidget({
    super.key,
    required this.item,
    required this.isLast,
    this.animationDelay = 0,
    required this.isOrderCompleted,
  });

  @override
  State<TimelineItemWidget> createState() => _TimelineItemWidgetState();
}

class _TimelineItemWidgetState extends State<TimelineItemWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _scaleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;

  Color get statusColor {
    switch (widget.item.title.toLowerCase()) {
      case 'open' || 'pending' || 'update' || 'in_progress':
        return Color.fromARGB(255, 234, 231, 57);
      case 'trigger_pending':
        //return Color(0xffEA7239);
        return Color.fromARGB(255, 234, 231, 57);
      case 'completed':
        return Color(0xff338AFF);
      case 'rejected':
        return Color(0xffD64242);
      case 'cancelled' || 'error' || 'unknown' || 'trigger_pending_with_error':
        return Color(0xffD64242);
      default:
        return Colors.grey;
    }
  }

  bool get shouldBlink {
    switch (widget.item.title.toLowerCase()) {
      case 'open':
      case 'pending':
      case 'update':
      case 'in_progress':
      case 'trigger_pending':
        return true;
      default:
        return false;
    }
  }

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    //size of the dot small to big animation
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    if (shouldBlink) {
      Future.delayed(Duration(milliseconds: widget.animationDelay), () {
        if (mounted) {
          _pulseController.repeat(reverse: true);
          _scaleController.repeat(reverse: true);
        }
      });
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    String formattedTime =
        widget.item.dateTime != null
            ? "${widget.item.dateTime!.hour}:${widget.item.dateTime!.minute}:${widget.item.dateTime!.second}"
            : "--";

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Column(
          children: [
            shouldBlink
                ? Stack(
                    alignment: Alignment.center,
                    children: [
                      // Static inner circle
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: statusColor.withOpacity(0.5),
                          shape: BoxShape.circle,
                        ),
                      ),

                      // Animated outer circle
                      AnimatedBuilder(
                        animation: Listenable.merge(
                            [_pulseAnimation, _scaleAnimation]),
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _scaleAnimation.value,
                            child: Opacity(
                              opacity: _pulseAnimation.value,
                              child: Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: statusColor,
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: statusColor.withOpacity(0.6),
                                      blurRadius: 6,
                                      spreadRadius: 2,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  )
                : Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: statusColor,
                      shape: BoxShape.circle,
                    ),
                  ),
            if (!widget.isLast) //line
              Container(
                width: 2,
                height: 48,
                color: widget.isOrderCompleted
                    ? ThemeConstants.blue
                    : ThemeConstants.zenWhite,
                margin: const EdgeInsets.symmetric(vertical: 1),
              ),
          ],
        ),
        const SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "${widget.item.title}  (${widget.item.orderType})",
              style: TextStyle(
                color: widget.item.isCompleted
                    ? Colors.white
                    : const Color(0xFF999999),
                fontSize: 13,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (widget.item.dateTime != null) ...[
              const SizedBox(height: 2),
              Row(
                children: [
                  const ImageIcon(
                    AssetImage("images/time.png"),
                    color: Color(0xffC5C5C5),
                    size: 12,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    formattedTime,
                    style: const TextStyle(
                      color: Color(0xffCDCDCD),
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    widget.item.transactionType,
                    style: const TextStyle(
                      color: Color(0xffCDCDCD),
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ],
            if (!widget.isLast) const SizedBox(height: 16),
          ],
        ),
        
        if (widget.item.totalFilledQuantity != null &&
            widget.item.totalQuantity != null) ...[
          const Spacer(),
          const ImageIcon(
            AssetImage("images/tile-generic/qty_icon.png"),
            color: Color(0xff338AFF),
            size: 12,
          ),
          const SizedBox(width: 4),
          Text(
            '${widget.item.totalFilledQuantity}/${widget.item.totalQuantity}',
            style: const TextStyle(
              color: Colors.grey,
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ],
    );
  }
}

class TimelineItem {
  final String title;
  final bool isCompleted;
  final DateTime? dateTime;
  final String transactionType;
  final String orderType;
  final int internalOrderId;
  final int totalFilledQuantity;
  final int totalQuantity;

  TimelineItem({
    required this.title,
    this.dateTime,
    required this.isCompleted,
    required this.transactionType,
    required this.orderType,
    required this.internalOrderId,
    required this.totalFilledQuantity,
    required this.totalQuantity,
  });
}
