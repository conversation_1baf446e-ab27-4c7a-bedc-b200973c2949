import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:phoenix/features/watchlist/data/watchlist_provider.dart';
import 'package:phoenix/features/watchlist/model/watchlist_model.dart';
import 'package:flutter/material.dart';
part 'watchist_event.dart';
part 'watchist_state.dart';

class WatchistBloc extends Bloc<WatchistEvent, WatchistState> {
  final WatchlistProvider _watchlistProvider;
  WatchistBloc(this._watchlistProvider) : super(WatchistInitial()) {
    on<WatchlistData>(_onWatchlistData);
  }

  void _onWatchlistData(WatchlistData event, Emitter<WatchistState> emit) async {
    emit(PnlLoading());
    try {
      final watchlistData = await _watchlistProvider.loadWatchlists(event.clientId.toString());
      //final pnlData = await _pnlDataProvider.fetchMockPnlDataFromFile();
      emit(WatchlistLoaded(watchlistData));
    } catch (e) {
      emit(WatchlistError(e.toString()));
    }
  }
}
