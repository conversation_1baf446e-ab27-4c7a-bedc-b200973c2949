import 'package:flutter/material.dart';

enum SortOption {
  alphabetical,
  percentChange,
  invested,
  date,
  lastTradedPrice,
  unrealized,
  realized,
}

class SortService {
  static final SortService _instance = SortService._internal();
  
  factory SortService() {
    return _instance;
  }
  
  SortService._internal();

  static SortService get instance => _instance;

  void showSortOptions<T>({
    required BuildContext context,
    required SortOption? currentSortOption,
    required bool isAscending,
    required Function(SortOption?, bool) onSortChanged,
    List<SortOption>? availableOptions,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xff1C1C1C),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildSortOptionsMenu(
        context: context,
        currentSortOption: currentSortOption,
        isAscending: isAscending,
        onSortChanged: onSortChanged,
        availableOptions: availableOptions,
      ),
    );
  }

  Widget _buildSortOptionsMenu({
    required BuildContext context,
    required SortOption? currentSortOption,
    required bool isAscending,
    required Function(SortOption?, bool) onSortChanged,
    List<SortOption>? availableOptions,
  }) {
    final options = availableOptions ?? SortOption.values;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (currentSortOption != null) ...[
            ListTile(
              leading: const Icon(Icons.clear_all, color: Colors.white),
              title: const Text('Clear Sort', 
                style: TextStyle(color: Colors.white)),
              onTap: () {
                onSortChanged(null, true);
                Navigator.pop(context);
              },
            ),
            const Divider(color: Colors.white24, height: 1),
          ],
          ...options.map((option) => _buildSortOptionTile(
            context: context,
            option: option,
            currentSortOption: currentSortOption,
            isAscending: isAscending,
            onSortChanged: onSortChanged,
          )),
        ],
      ),
    );
  }

  Widget _buildSortOptionTile({
    required BuildContext context,
    required SortOption option,
    required SortOption? currentSortOption,
    required bool isAscending,
    required Function(SortOption?, bool) onSortChanged,
  }) {
    final sortConfig = _getSortOptionConfig(option);

    return ListTile(
      leading: sortConfig.icon,
      title: Text(sortConfig.title, 
        style: const TextStyle(color: Colors.white)),
      onTap: () {
        final newIsAscending = currentSortOption == option ? !isAscending : true;
        onSortChanged(option, newIsAscending);
        Navigator.pop(context);
      },
      trailing: currentSortOption == option
          ? Icon(
              isAscending ? Icons.arrow_upward : Icons.arrow_downward,
              color: Colors.white,
            )
          : null,
    );
  }

  _SortOptionConfig _getSortOptionConfig(SortOption option) {
    switch (option) {
      case SortOption.alphabetical:
        return _SortOptionConfig(
          title: 'A-Z Alphabetically',
          icon: const Icon(Icons.sort_by_alpha, color: Colors.white),
        );
      case SortOption.percentChange:
        return _SortOptionConfig(
          title: 'Change',
          icon: const Text('%', 
            style: TextStyle(color: Colors.white, fontSize: 20)),
        );
      case SortOption.invested:
        return _SortOptionConfig(
          title: 'Invested',
          icon: const Icon(Icons.currency_rupee, color: Colors.white),
        );
      case SortOption.date:
        return _SortOptionConfig(
          title: 'Date',
          icon: const Icon(Icons.date_range, color: Colors.white),
        );
      case SortOption.lastTradedPrice:
        return _SortOptionConfig(
          title: 'Last Traded Price',
          icon: const Text('LTP', 
            style: TextStyle(color: Colors.white, fontSize: 16)),
        );
      case SortOption.unrealized:
        return _SortOptionConfig(
          title: 'Unrealized Price',
          icon: const Icon(Icons.punch_clock_outlined, color: Colors.white),
        );
      case SortOption.realized:
        return _SortOptionConfig(
          title: 'Realized Price',
          icon: const Icon(Icons.trending_up, color: Colors.white),
        );

    }
  }

  List<T> sortList<T>({
    required List<T> items,
    required SortOption? sortOption,
    required bool isAscending,
    required Map<SortOption, Comparable Function(T)> sortFunctions,
  }) {
    if (sortOption == null || !sortFunctions.containsKey(sortOption)) {
      return items;
    }

    return List<T>.from(items)..sort((a, b) {
      final comparison = sortFunctions[sortOption]!(a)
          .compareTo(sortFunctions[sortOption]!(b));
      return isAscending ? comparison : -comparison;
    });
  }
}

class _SortOptionConfig {
  final String title;
  final Widget icon;

  _SortOptionConfig({
    required this.title,
    required this.icon,
  });
}