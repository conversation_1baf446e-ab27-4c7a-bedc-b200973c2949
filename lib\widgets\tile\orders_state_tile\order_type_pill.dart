import 'package:flutter/material.dart';
import 'package:phoenix/utils/theme_constants.dart';

class OrderTypePill extends StatelessWidget {
  final String orderType;

  const OrderTypePill({super.key, required this.orderType});

  String _getOrderTypeText(String orderType) {
    switch (orderType) {
      case 'MARKET':
        return 'M';
      case 'LIMIT':
        return 'L';
      case 'STANDALONE_SL_LIMIT':
        return 'SL-L';
      case 'STANDALONE_SL_MARKET':
        return 'SL-M';
      case 'LIMIT_ORDER_WITH_SL_LIMIT':
        return 'L SL-L';
      case 'MARKET_ORDER_WITH_SL_LIMIT':
        return 'M SL-L';
      case 'LIMIT_ORDER_WITH_SL_MARKET':
        return 'L SL-M';
      case 'MARKET_ORDER_WITH_SL_MARKET':
        return 'M SL-M';
      case 'TRAILING_STOP_LOSS_MARKET_ORDER':
        return 'T SL-M';
      case 'TRAILING_STOP_LOSS_LIMIT_ORDER':
        return 'T SL-L';
      default:
        return orderType;
    }
  }

  String _getFullOrderTypeName(String orderType) {
    switch (orderType) {
      case 'MARKET':
        return 'Market Order';
      case 'LIMIT':
        return 'Limit Order';
      case 'STANDALONE_SL_LIMIT':
        return 'Standalone Stop Loss Limit';
      case 'STANDALONE_SL_MARKET':
        return 'Standalone Stop Loss Market';
      case 'LIMIT_ORDER_WITH_SL_LIMIT':
        return 'Limit Order with Stop Loss Limit';
      case 'MARKET_ORDER_WITH_SL_LIMIT':
        return 'Market Order with Stop Loss Limit';
      case 'LIMIT_ORDER_WITH_SL_MARKET':
        return 'Limit Order with Stop Loss Market';
      case 'MARKET_ORDER_WITH_SL_MARKET':
        return 'Market Order with Stop Loss Market';
      case 'TRAILING_STOP_LOSS_MARKET_ORDER':
        return 'Trailing Stop Loss Market Order';
      case 'TRAILING_STOP_LOSS_LIMIT_ORDER':
        return 'Trailing Stop Loss Limit Order';
      default:
        return orderType.replaceAll('_', ' ').toLowerCase().split(' ').map((word) =>
          word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : word).join(' ');
    }
  }


  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: _getFullOrderTypeName(orderType),
      
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(8),
      ),
      textStyle: const TextStyle(
        color: Colors.white,
        fontSize: 12,
        fontWeight: FontWeight.w400,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: ThemeConstants.blue.withValues(alpha: 0.5),
            width: 1,
          ),
          color: ThemeConstants.blue.withValues(alpha: 0.2),
        ),
        child: Text(
          _getOrderTypeText(orderType),
          textAlign: TextAlign.center,
          style: const TextStyle(
            color: ThemeConstants.zenWhite,
            fontSize: 11,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }
}
