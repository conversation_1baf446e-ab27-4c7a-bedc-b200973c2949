import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';
import '../../utils/theme_constants.dart';

class StrategyFlowchartView extends StatelessWidget {
  final String jsonText;

  const StrategyFlowchartView({super.key, required this.jsonText});

  @override
  Widget build(BuildContext context) {
    Map<String, dynamic> strategyData;
    try {
      strategyData = jsonDecode(jsonText);
    } catch (e) {
      return _buildErrorView(context);
    }

    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Scaffold(
          backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
          appBar: AppBar(
            backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
            elevation: 0,
            centerTitle: true,
            title: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.account_tree, color: AppTheme.primaryColor(themeState.isDarkMode), size: 28),
                SizedBox(width: 8),
                Text(
                  'Strategy Flowchart',
                  style: TextStyle(
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
              ],
            ),
            leading: IconButton(
              icon: Icon(Icons.arrow_back_ios_new_rounded, color: AppTheme.textPrimary(themeState.isDarkMode)),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.backgroundColor(themeState.isDarkMode),
                  AppTheme.cardColor(themeState.isDarkMode).withOpacity(0.7)
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: SingleChildScrollView(
              padding: EdgeInsets.all(20),
              child: Column(
                children: [
                  _buildUnderlyingSection(strategyData, themeState),
                  SizedBox(height: 20),
                  _buildArrow(themeState),
                  SizedBox(height: 20),
                  _buildEntryConditionSection(strategyData, themeState),
                  SizedBox(height: 20),
                  _buildArrow(themeState),
                  SizedBox(height: 20),
                  _buildExitConditionSection(strategyData, themeState),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorView(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Scaffold(
          backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
          appBar: AppBar(
            backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
            elevation: 0,
            title: Text('Error', style: TextStyle(color: AppTheme.textPrimary(themeState.isDarkMode))),
            leading: IconButton(
              icon: Icon(Icons.arrow_back_ios_new_rounded, color: AppTheme.textPrimary(themeState.isDarkMode)),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.red),
                SizedBox(height: 16),
                Text(
                  'Invalid JSON Format',
                  style: TextStyle(
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Unable to parse the strategy JSON',
                  style: TextStyle(
                    color: AppTheme.textSecondary(themeState.isDarkMode),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUnderlyingSection(Map<String, dynamic> strategyData, ThemeState themeState) {
    final underlying = strategyData['underlying'] as List<dynamic>?;
    return _buildCard(
      themeState: themeState,
      title: 'Underlying Assets',
      icon: Icons.trending_up,
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (underlying != null && underlying.isNotEmpty)
            ...underlying.map((asset) => Padding(
                  padding: EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      Icon(Icons.fiber_manual_record, size: 8, color: AppTheme.primaryColor(themeState.isDarkMode)),
                      SizedBox(width: 8),
                      Text(
                        asset.toString(),
                        style: TextStyle(
                          color: AppTheme.textPrimary(themeState.isDarkMode),
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ))
          else
            Text(
              'No underlying assets specified',
              style: TextStyle(
                color: AppTheme.textSecondary(themeState.isDarkMode),
                fontSize: 14,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEntryConditionSection(Map<String, dynamic> strategyData, ThemeState themeState) {
    final entryCondition = strategyData['entryCondition'] as Map<String, dynamic>?;
    return _buildConditionCard(
      themeState: themeState,
      title: 'Entry Condition',
      icon: Icons.login,
      iconColor: Colors.green,
      condition: entryCondition,
      actionType: 'ENTRY',
    );
  }

  Widget _buildExitConditionSection(Map<String, dynamic> strategyData, ThemeState themeState) {
    final exitCondition = strategyData['exitCondition'] as Map<String, dynamic>?;
    return _buildConditionCard(
      themeState: themeState,
      title: 'Exit Condition',
      icon: Icons.logout,
      iconColor: Colors.red,
      condition: exitCondition,
      actionType: 'EXIT',
    );
  }

  Widget _buildConditionCard({
    required ThemeState themeState,
    required String title,
    required IconData icon,
    required Color iconColor,
    required Map<String, dynamic>? condition,
    required String actionType,
  }) {
    return _buildCard(
      themeState: themeState,
      title: title,
      icon: icon,
      iconColor: iconColor,
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (condition != null) ...[
            _buildConditionDetails(condition, themeState),
            SizedBox(height: 16),
            _buildActionDetails(
              (condition['content'] != null ? condition['content']['action'] : condition['action']) as Map<String, dynamic>?,
              themeState,
            ),
          ] else
            Text(
              'No $actionType condition specified',
              style: TextStyle(
                color: AppTheme.textSecondary(themeState.isDarkMode),
                fontSize: 14,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildConditionDetails(Map<String, dynamic> condition, ThemeState themeState) {
    final content = condition['content'] as Map<String, dynamic>?;
    if (content == null) return SizedBox();

    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: themeState.isDarkMode 
            ? ThemeConstants.zenBlack1.withOpacity(0.3)
            : AppTheme.backgroundColor(themeState.isDarkMode).withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.rule, size: 16, color: AppTheme.primaryColor(themeState.isDarkMode)),
              SizedBox(width: 8),
              Text(
                'Condition Logic',
                style: TextStyle(
                  color: AppTheme.primaryColor(themeState.isDarkMode),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            'Type:  [1m${condition['type']?.toString().toUpperCase() ?? 'N/A'}',
            style: TextStyle(
              color: ThemeConstants.zenWhite,
              fontSize: 14,
            ),
          ),
          SizedBox(height: 4),
          Text(
            'Operator: ${condition['operator'] ?? 'N/A'}',
            style: TextStyle(
              color: AppTheme.textPrimary(themeState.isDarkMode),
              fontSize: 14,
            ),
          ),
          SizedBox(height: 8),
          _buildConditionsList(content['conditions'] as List<dynamic>?, themeState),
        ],
      ),
    );
  }

  Widget _buildConditionsList(List<dynamic>? conditions, ThemeState themeState) {
    if (conditions == null || conditions.isEmpty) {
      return Text(
        'No conditions specified',
        style: TextStyle(
          color: AppTheme.textSecondary(themeState.isDarkMode),
          fontSize: 12,
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: conditions.map((operand) {
        final operandMap = operand as Map<String, dynamic>?;
        if (operandMap == null) return SizedBox();

        final type = operandMap['type']?.toString() ?? 'UNKNOWN';
        final timeValue = operandMap['timeValue']?.toString() ?? 'N/A';
        final format = operandMap['format']?.toString() ?? '';

        return Container(
          margin: EdgeInsets.only(top: 8),
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: themeState.isDarkMode 
                ? ThemeConstants.zenBlack.withOpacity(0.5)
                : AppTheme.surfaceColor(themeState.isDarkMode),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            children: [
              Icon(
                type == 'timeCondition' ? Icons.access_time : Icons.rule,
                size: 14,
                color: Colors.orange,
              ),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  '${type.toUpperCase()}: $timeValue${format.isNotEmpty ? ' ($format)' : ''}',
                  style: TextStyle(
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildActionDetails(Map<String, dynamic>? action, ThemeState themeState) {
    if (action == null) return SizedBox();

    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: themeState.isDarkMode 
            ? ThemeConstants.zenBlack1.withOpacity(0.3)
            : AppTheme.backgroundColor(themeState.isDarkMode).withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.play_arrow, size: 16, color: Colors.orange),
              SizedBox(width: 8),
              Text(
                'Action Details',
                style: TextStyle(
                  color: Colors.orange,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          _buildActionItem('Order Type', action['orderType'], themeState),
          _buildActionItem('Transaction', action['transactionType'], themeState),
          _buildActionItem('Quantity', action['qty'], themeState),
          _buildActionItem('Product Type', action['productType'], themeState),
        ],
      ),
    );
  }

  Widget _buildActionItem(String label, dynamic value, ThemeState themeState) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          SizedBox(width: 16),
          Icon(Icons.arrow_right, size: 12, color: AppTheme.textSecondary(themeState.isDarkMode)),
          SizedBox(width: 4),
          Text(
            '$label: ',
            style: TextStyle(
              color: AppTheme.textSecondary(themeState.isDarkMode),
              fontSize: 12,
            ),
          ),
          Text(
            value?.toString().toUpperCase() ?? 'N/A',
            style: TextStyle(
              color: AppTheme.textPrimary(themeState.isDarkMode),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCard({
    required ThemeState themeState,
    required String title,
    required IconData icon,
    Color? iconColor,
    required Widget content,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppTheme.cardColor(themeState.isDarkMode).withOpacity(0.8),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.borderColor(themeState.isDarkMode),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: themeState.isDarkMode ? Colors.black.withOpacity(0.3) : Colors.grey.withOpacity(0.2),
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: iconColor ?? AppTheme.primaryColor(themeState.isDarkMode), size: 24),
                SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    color: AppTheme.textPrimary(themeState.isDarkMode),
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            content,
          ],
        ),
      ),
    );
  }

  Widget _buildArrow(ThemeState themeState) {
    return Column(
      children: [
        Container(
          width: 2,
          height: 20,
          color: AppTheme.primaryColor(themeState.isDarkMode).withOpacity(0.5),
        ),
        Icon(
          Icons.keyboard_arrow_down,
          color: AppTheme.primaryColor(themeState.isDarkMode),
          size: 32,
        ),
      ],
    );
  }
}