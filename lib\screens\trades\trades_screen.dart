import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/broker_data_map/bloc/broker_data_map_bloc.dart';
import 'package:phoenix/features/common/broker_account_strategy_data.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/features/trades/bloc/trades_bloc.dart';
import 'package:phoenix/features/trades/bloc/trades_event.dart';
import 'package:phoenix/features/trades/bloc/trades_state.dart';
import 'package:phoenix/features/trades/data/trades_data_provider.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/widgets/circular_loader.dart';
import 'package:phoenix/widgets/tile/trade_child.dart';
import 'package:phoenix/widgets/date_range_selector.dart';

class TradesScreen extends StatefulWidget {
  const TradesScreen({super.key});

  @override
  State<TradesScreen> createState() => _TradesScreenState();
}

class _TradesScreenState extends State<TradesScreen> {
  DateTime? _currentStartDate;
  DateTime? _currentEndDate;

  @override
  void initState() {
    super.initState();
    // Set default dates (today)
    final today = DateTime.now();
    _currentStartDate = DateTime(today.year, today.month, today.day);
    _currentEndDate = DateTime(today.year, today.month, today.day);
    // Initial load will be handled by DateRangeSelector
  }

  void _fetchTradesData({DateTime? startDate, DateTime? endDate}) {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      String? startTimestamp;
      String? endTimestamp;
      
      if (startDate != null && endDate != null) {
        startTimestamp = "${startDate.year.toString().padLeft(4, '0')}-${startDate.month.toString().padLeft(2, '0')}-${startDate.day.toString().padLeft(2, '0')} 00:00:00";
        endTimestamp = "${endDate.year.toString().padLeft(4, '0')}-${endDate.month.toString().padLeft(2, '0')}-${endDate.day.toString().padLeft(2, '0')} 23:59:59";
      }
      
      context.read<TradesBloc>().add(FetchTradesData(
        authState.credentialsModel.clientId,
        startTimestamp: startTimestamp,
        endTimestamp: endTimestamp,
      ));
    }
  }

  void _onDateRangeChanged(DateTime startDate, DateTime endDate) {
    setState(() {
      _currentStartDate = startDate;
      _currentEndDate = endDate;
    });
    _fetchTradesData(startDate: startDate, endDate: endDate);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Scaffold(
          backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
          appBar: AppBar(
            title: Text(
              'Trades',
              style: TextStyle(
                color: AppTheme.textPrimary(themeState.isDarkMode),
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
            elevation: 0,
            iconTheme: IconThemeData(color: AppTheme.textPrimary(themeState.isDarkMode)),
          ),
        body: Column(
          children: [
            // Date Range Selector with improved layout
            Container(
              margin: const EdgeInsets.fromLTRB(16, 12, 16, 8),
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
              child: Row(
                children: [
                  // Title and date selector
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 4, bottom: 8),
                          child: Text(
                            'Date Range',
                            style: TextStyle(
                              color: AppTheme.textSecondary(themeState.isDarkMode),
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              letterSpacing: 0.3,
                            ),
                          ),
                        ),
                        DateRangeSelector(
                          onDateRangeChanged: _onDateRangeChanged,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Refresh button
                  GestureDetector(
                    onTap: () => _fetchTradesData(startDate: _currentStartDate, endDate: _currentEndDate),
                    child: Container(
                      margin: const EdgeInsets.only(top: 28),
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xff2D3035).withOpacity(0.6),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: const Color(0xff404449).withOpacity(0.5),
                        ),
                      ),
                      child: Icon(
                        Icons.refresh_rounded,
                        color: ThemeConstants.zenWhite.withOpacity(0.7),
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Trades List
            Expanded(
              child: BlocBuilder<TradesBloc, TradesState>(
                builder: (context, state) {
                  if (state is TradesLoading) {
                    return const Center(child: CircularLoader());
                  } else if (state is TradesLoaded) {
                    if (state.tradesData.isEmpty) {
                      return Center(
                        child: Text(
                          'No trades available',
                          style: TextStyle(
                            color: AppTheme.textPrimary(themeState.isDarkMode),
                            fontSize: 16,
                          ),
                        ),
                      );
                    }
                    return BlocBuilder<BrokerDataMapBloc, BrokerDataMapState>(
                      builder: (context, brokerState) {
                        return ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: state.tradesData.length,
                          itemBuilder: (context, index) {
                            final trade = state.tradesData[index];
                            debugPrint(state.tradesData[0].symbol);
                            // Get broker account strategy data
                            BrokerAccountStrategyData? brokerAccountStrategyData;
                            if (brokerState is BrokerDataMapProcessedState) {
                              final brokerName = brokerState.brokerNameToLabelMap[trade.broker] ?? "N/A";
                              final accountName = brokerState.accountIdToNameMap[trade.accountId] ?? "N/A";
                              final strategyName = brokerState.strategyIdToNameMap[trade.strategyId] ?? "N/A";
                              
                              brokerAccountStrategyData = BrokerAccountStrategyData(
                                brokerName: brokerName,
                                accountId: trade.accountId,
                                strategyId: trade.strategyId,
                                accountName: accountName,
                                strategyName: strategyName,
                              );
                            }

                            return Container(
                              margin: const EdgeInsets.only(bottom: 12),
                              child: TradeChild(
                                data: trade,
                                brokerAccountStrategyData: brokerAccountStrategyData,
                              ),
                            );
                          },
                        );
                      },
                    );
                  } else if (state is TradesError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 48,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            state.message,
                            style: TextStyle(
                              color: Colors.red,
                              fontSize: 16,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () => _fetchTradesData(),
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),
          ],
        ),
        );
      },
    );
  }
}