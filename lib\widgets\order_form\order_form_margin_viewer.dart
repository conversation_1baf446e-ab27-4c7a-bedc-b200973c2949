import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/broker_account_strategy_selection/bloc/broker_account_strategy_selection_bloc.dart';
import 'package:phoenix/features/margin_state/bloc/margin_state_bloc.dart';
import 'package:phoenix/utils/util_functions.dart';
import 'package:phoenix/widgets/circular_loader.dart';

class OrferFormMarginViewer extends StatefulWidget {
  final int? accountId; // Optional param for direct fetching

  const OrferFormMarginViewer({super.key, this.accountId});

  @override
  State<OrferFormMarginViewer> createState() => _OrferFormMarginViewerState();
}

class _OrferFormMarginViewerState extends State<OrferFormMarginViewer> {
  @override
  void initState() {
    super.initState();
    debugPrint("🚀 Init of Margin Viewer ran");
    
    if (widget.accountId != null) {
      // If accountId is provided, fetch margin directly
      _fetchMargin(context, widget.accountId);
    } else {
      // Otherwise, fetch based on the selected account in the bloc
      _fetchMargin(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    double dynamicWidth = MediaQuery.of(context).size.width * 0.40;

    return BlocListener<BrokerAccountStrategySelectionBloc, BrokerAccountStrategyState>(
      listenWhen: (previous, current) => previous.selectedAccount != current.selectedAccount,
      listener: (context, state) {
        if (widget.accountId == null) {
          _fetchMargin(context);
        }
      },
      child: BlocBuilder<MarginBloc, MarginState>(
        builder: (context, marginState) {
          return SizedBox(
            width: dynamicWidth,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (marginState is MarginLoading)
                  Center(
                    child: Transform.scale(
                      scale: 0.5,
                      child: CircularLoader(),
                    ),
                  )
                else if (marginState is MarginError)
                  Expanded(
                    child: Text(
                      'Error: ${marginState.error}',
                      style: TextStyle(color: Colors.red),
                      overflow: TextOverflow.ellipsis,
                    ),
                  )
                else if (marginState is MarginLoaded)
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        _fetchMargin(context);
                      },
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          '₹${UtilFunctions.formatIndianCurrency(marginState.margin)}',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 17,
                            fontWeight: FontWeight.w600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ),
                // IconButton(
                //   icon: Icon(
                //     Icons.refresh,
                //     color: ThemeConstants.blue,
                //   ),
                //   onPressed: () {
                //     _fetchMargin(context);
                //   },
                // ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _fetchMargin(BuildContext context, [int? overrideAccountId]) {
    final authState = context.read<AuthBloc>().state; // Get clientId
    final selectionState = context.read<BrokerAccountStrategySelectionBloc>().state; // Get accountId

    if (authState is AuthAuthenticated) {
      final int? clientId = authState.credentialsModel.clientId;
      final int? accountId = overrideAccountId ?? selectionState.selectedAccount?.accountId;

      if (clientId != null && accountId != null) {
        debugPrint("Fetching margin for Client: $clientId, Account: $accountId");
        context.read<MarginBloc>().add(
              MarginFetchEvent(clientId, accountId),
            );
      }
    }
  }
}
