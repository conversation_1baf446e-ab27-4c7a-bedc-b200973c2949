import 'package:flutter/material.dart';
import 'package:phoenix/home.dart';
import 'package:phoenix/screens/login/login.dart';
import 'package:phoenix/screens/account/fund_profile_page.dart';
import 'package:phoenix/screens/settings/settings_page.dart';
import 'package:phoenix/screens/splash/splashscreen.dart';
import 'package:phoenix/screens/trades/trades_screen.dart';
import 'package:phoenix/screens/option_chain/option_chain_screen.dart';

class RouterGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case '/':
        return MaterialPageRoute(builder: (_) => SplashScreen());

      case '/home':
        // Ensure arguments are passed as a Map
        return MaterialPageRoute(
          builder: (_) => Home(),
        );

      case '/login':
        return MaterialPageRoute(
          builder: (_) => Login(),
        );
      case '/account':
        return MaterialPageRoute(
          builder: (_) => FundProfilePage(),
        );
      case '/settings':
        return MaterialPageRoute(
          builder: (_) => SettingsPage(),
        );
      case '/trades':
        return MaterialPageRoute(
          builder: (_) => TradesScreen(),
        );
      case '/option-chain':
        return MaterialPageRoute(
          builder: (_) => OptionChainScreen(),
        );

      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            body: Center(child: Text('No route defined for ${settings.name}')),
          ),
        );
    }
  }
}
