import 'dart:convert';
import 'package:flutter/material.dart';
import '../../utils/theme_constants.dart';
import 'strategy_flowchart_view.dart';
import 'json_viewer_page.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/utils/app_theme.dart';

class FlowchartMessageBubble extends StatelessWidget {
  final String jsonText;
  final void Function()? onDeploy;
  final TextSpan Function(String) highlightJson;
  final String Function(String) prettyPrintJson;
  final VoidCallback? onViewJson; // Add this line

  const FlowchartMessageBubble({
    super.key,
    required this.jsonText,
    required this.onDeploy,
    required this.highlightJson,
    required this.prettyPrintJson,
    this.onViewJson, // Add this line
  });

  Map<String, dynamic>? _parseJson() {
    try {
      return jsonDecode(jsonText);
    } catch (e) {
      return null;
    }
  }

  void _viewJsonPage(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => JsonViewerPage(
          jsonText: jsonText,
          highlightJson: highlightJson,
          prettyPrintJson: prettyPrintJson,
        ),
      ),
    );
  }

  void _viewFullFlowchart(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => StrategyFlowchartView(
          jsonText: jsonText,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final strategyData = _parseJson();
    
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        final isDark = themeState.isDarkMode;
        final cardColor = AppTheme.cardColor(isDark);
        final borderColor = AppTheme.borderColor(isDark);
        final headerColor = isDark ? const Color(0xFF23272f) : AppTheme.surfaceColor(isDark);
        final contentColor = isDark ? const Color(0xFF181b20) : AppTheme.backgroundColor(isDark);
        final textColor = AppTheme.textPrimary(isDark);
        final secondaryTextColor = AppTheme.textSecondary(isDark);
        final blue = AppTheme.primaryColor(isDark);
        return Align(
          alignment: Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
            decoration: BoxDecoration(
              color: cardColor,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: borderColor, width: 1),
              boxShadow: [
                BoxShadow(
                  color: isDark ? Colors.black26 : Colors.grey.withOpacity(0.2),
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header bar
                Container(
                  decoration: BoxDecoration(
                    color: headerColor,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.account_tree, color: blue, size: 18),
                          SizedBox(width: 8),
                          Text(
                            'Strategy Flowchart',
                            style: TextStyle(
                              color: secondaryTextColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ],
                      ),
                      if (onViewJson != null)
                        Container(
                          decoration: BoxDecoration(
                            color: blue.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: IconButton(
                            icon: Icon(Icons.data_object, size: 16, color: blue),
                            tooltip: 'View JSON',
                            padding: EdgeInsets.all(6),
                            constraints: BoxConstraints(),
                            onPressed: onViewJson,
                          ),
                        ),
                    ],
                  ),
                ),
                // Flowchart content area
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: contentColor,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),
                  ),
                  padding: const EdgeInsets.all(16),
                  child: strategyData != null
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildMiniFlowchart(strategyData),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    icon: const Icon(Icons.fullscreen, size: 18),
                                    label: const Text('View Full Chart'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: headerColor,
                                      foregroundColor: secondaryTextColor,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      padding: EdgeInsets.symmetric(vertical: 12),
                                    ),
                                    onPressed: () => _viewFullFlowchart(context),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    icon: const Icon(Icons.cloud_upload, size: 18),
                                    label: const Text('Deploy'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: blue.withOpacity(0.2),
                                      foregroundColor: blue,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      padding: EdgeInsets.symmetric(vertical: 12),
                                    ),
                                    onPressed: onDeploy,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        )
                      : _buildErrorView(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMiniFlowchart(Map<String, dynamic> strategyData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildMiniCard(
          'Underlying Assets',
          Icons.trending_up,
          _buildUnderlyingContent(strategyData['underlying']),
        ),
        SizedBox(height: 12),
        _buildMiniArrow(),
        SizedBox(height: 12),
        _buildMiniCard(
          'Entry Condition',
          Icons.login,
          _buildConditionContent(strategyData['entryCondition']),
          Colors.green,
        ),
        SizedBox(height: 12),
        _buildMiniArrow(),
        SizedBox(height: 12),
        _buildMiniCard(
          'Exit Condition',
          Icons.logout,
          _buildConditionContent(strategyData['exitCondition']),
          Colors.red,
        ),
      ],
    );
  }

  Widget _buildMiniCard(String title, IconData icon, Widget content, [Color? iconColor]) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        final isDark = themeState.isDarkMode;
        final cardColor = isDark ? AppTheme.cardColor(true).withOpacity(0.7) : Colors.white;
        final borderColor = iconColor != null ? iconColor.withOpacity(0.3) : AppTheme.primaryColor(isDark).withOpacity(0.3);
        final textColor = AppTheme.textPrimary(isDark);
        final iconCol = iconColor ?? AppTheme.primaryColor(isDark);
        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: cardColor,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: borderColor),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(icon, color: iconCol, size: 16),
                  SizedBox(width: 8),
                  Text(
                    title,
                    style: TextStyle(
                      color: textColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8),
              content,
            ],
          ),
        );
      },
    );
  }

  Widget _buildUnderlyingContent(dynamic underlying) {
    if (underlying is List && underlying.isNotEmpty) {
      return Wrap(
        spacing: 8,
        runSpacing: 4,
        children: underlying.take(3).map((asset) => Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: ThemeConstants.blue.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            asset.toString(),
            style: TextStyle(
              color: ThemeConstants.blue,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        )).toList(),
      );
    }
    return Text(
      'No assets specified',
      style: TextStyle(
        color: ThemeConstants.zenWhite.withOpacity(0.7),
        fontSize: 12,
      ),
    );
  }

  Widget _buildConditionContent(dynamic condition) {
    if (condition is Map<String, dynamic>) {
      final type = condition['type']?.toString() ?? 'N/A';
      final operator = condition['operator']?.toString() ?? 'N/A';
      final operands = condition['operands'] as List<dynamic>?;
      final operandCount = operands?.length ?? 0;
      final action = condition['action'] as Map<String, dynamic>?;
      
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Type: $type',
            style: TextStyle(
              color: ThemeConstants.zenWhite,
              fontSize: 12,
            ),
          ),
          SizedBox(height: 2),
          Text(
            'Operator: $operator',
            style: TextStyle(
              color: ThemeConstants.zenWhite.withOpacity(0.8),
              fontSize: 11,
            ),
          ),
          SizedBox(height: 2),
          Text(
            '$operandCount condition${operandCount != 1 ? 's' : ''} defined',
            style: TextStyle(
              color: ThemeConstants.zenWhite.withOpacity(0.7),
              fontSize: 11,
            ),
          ),
          if (action != null) ...[
            SizedBox(height: 4),
            Text(
              'Action: ${action['transactionType']?.toString().toUpperCase() ?? 'N/A'} ${action['qty']?.toString() ?? 'N/A'} ${action['tradingSymbol'] ?? 'N/A'}',
              style: TextStyle(
                color: Colors.orange.withOpacity(0.8),
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      );
    }
    return Text(
      'No condition specified',
      style: TextStyle(
        color: ThemeConstants.zenWhite.withOpacity(0.7),
        fontSize: 12,
      ),
    );
  }

  Widget _buildMiniArrow() {
    return Center(
      child: Icon(
        Icons.keyboard_arrow_down,
        color: ThemeConstants.blue.withOpacity(0.7),
        size: 20,
      ),
    );
  }

  Widget _buildErrorView() {
    return Column(
      children: [
        Icon(Icons.error_outline, size: 32, color: Colors.red.withOpacity(0.7)),
        SizedBox(height: 8),
        Text(
          'Invalid JSON Format',
          style: TextStyle(
            color: ThemeConstants.zenWhite,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 4),
        Text(
          'Unable to parse the strategy JSON',
          style: TextStyle(
            color: ThemeConstants.zenWhite.withOpacity(0.7),
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}