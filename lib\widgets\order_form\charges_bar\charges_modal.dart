import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/services/comissions_charges_service.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/theme_constants.dart';

class CalculatedChargesModal {
  final double brokerageCharge;
  final double sttCttCharge;
  final double transactionCharge;
  final double ipftCharge;
  final double sebiCharge;
  final double stampCharge;
  final double gstCharge;
  final double exchangeCharge;
  final double finalCharges;

  CalculatedChargesModal({
    required this.brokerageCharge,
    required this.sttCttCharge,
    required this.transactionCharge,
    required this.ipftCharge,
    required this.sebiCharge,
    required this.stampCharge,
    required this.gstCharge,
    required this.exchangeCharge,
    required this.finalCharges,
  });
}

class ChargesModal extends StatelessWidget {
  final CalculatedCharges charges;
  final double? requiredMargin;
  final double? availableMargin;

  const ChargesModal({
    Key? key,
    required this.charges,
    this.requiredMargin,
    this.availableMargin,
  }) : super(key: key);

  String formatCurrency(double amount) {
    return '₹$amount';
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Dialog(
          backgroundColor: AppTheme.cardColor(themeState.isDarkMode),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: AppTheme.borderColor(themeState.isDarkMode),
              width: 1,
            ),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            constraints: const BoxConstraints(maxWidth: 400),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Text(
                      'Charges and taxes',
                      style: TextStyle(
                        color: AppTheme.textPrimary(themeState.isDarkMode),
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Spacer(),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Container(
                        padding: const EdgeInsets.all(3),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(100),
                          color: AppTheme.borderColor(themeState.isDarkMode).withOpacity(0.3),
                        ),
                        child: Icon(
                          Icons.close,
                          color: AppTheme.textPrimary(themeState.isDarkMode),
                          size: 18,
                        ),
                      ),
                    )
                  ],
                ),
                const SizedBox(height: 20),

                // Charges List
                _buildChargeItem('Brokerage', charges.brokerageCharge, themeState),
                _buildChargeItem('SEBI turnover fee', charges.sebiCharge, themeState),
                _buildChargeItem('Stamp duty', charges.stampCharge, themeState),
                _buildChargeItem('Transaction charges', charges.transactionCharge, themeState),
                _buildChargeItem('IPFT Charge', charges.ipftCharge, themeState),
                _buildChargeItem('Transaction tax (CTT/STT)', charges.sttCttCharge, themeState),
                _buildChargeItem('GST', charges.gstCharge, themeState),

                // Divider
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  height: 1,
                  color: AppTheme.borderColor(themeState.isDarkMode),
                ),

                // Total Charges
                _buildChargeItem(
                  'Total charges',
                  charges.finalCharges,
                  themeState,
                  isTotal: true,
                ),

                // Margin Information (if provided)
                if (requiredMargin != null) ...[
                  const SizedBox(height: 8),
                  _buildChargeItem('Required margin', requiredMargin!, themeState),
                ],
                if (availableMargin != null) ...[
                  const SizedBox(height: 8),
                  _buildChargeItem('Available margin', availableMargin!, themeState),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildChargeItem(String label, double value, ThemeState themeState, {bool isTotal = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 6),
      decoration: BoxDecoration(
        color: AppTheme.cardColor(themeState.isDarkMode),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: isTotal 
                  ? AppTheme.textPrimary(themeState.isDarkMode)
                  : AppTheme.textSecondary(themeState.isDarkMode),
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.w500 : FontWeight.normal,
            ),
          ),
          Text(
            formatCurrency(value),
            style: TextStyle(
              color: AppTheme.textPrimary(themeState.isDarkMode),
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Static method to show the modal
  static Future<void> show(
    BuildContext context, {
    required CalculatedCharges charges,
    double? requiredMargin,
    double? availableMargin,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return ChargesModal(
          charges: charges,
          requiredMargin: requiredMargin,
          availableMargin: availableMargin,
        );
      },
    );
  }
}
