import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/authentication/data/data_provider/auth_service.dart';
import 'package:phoenix/features/authentication/data/repository/auth_repository.dart';
import 'package:phoenix/features/bottom_navigation/bloc/bottom_navigation_bloc.dart';
import 'package:phoenix/features/broker_data_map/bloc/broker_data_map_bloc.dart';
import 'package:phoenix/features/commissions_data_map/bloc/commissions_data_map_bloc.dart';
import 'package:phoenix/features/commissions_data_map/provider/commissions_provider.dart';
import 'package:phoenix/features/fetch_clients_data/bloc/clients_data_bloc.dart';
import 'package:phoenix/features/fetch_clients_data/data/provider/clients_provider.dart';
import 'package:phoenix/features/fetch_clients_data/data/repo/clients_repo.dart';
import 'package:phoenix/features/latest_zen_order_state/bloc/latest_zen_order_state_bloc.dart';
import 'package:phoenix/features/latest_zen_order_state/data/provider/latest_zen_order_state_provider.dart';
import 'package:phoenix/features/margin_state/bloc/margin_state_bloc.dart';
import 'package:phoenix/features/margin_state/data/provider/margin_provider.dart';
import 'package:phoenix/features/orders/bloc/orders_bloc.dart';
import 'package:phoenix/features/orders/data/provider/order_form_provider.dart';
import 'package:phoenix/features/orders/data/repository/order_form_repository.dart';
import 'package:phoenix/features/orders_state/bloc/orders_state_bloc.dart';
import 'package:phoenix/features/orders_state/data/provider/order_state_provider.dart';
import 'package:phoenix/features/orders_state/data/repository/order_state_repository.dart';
import 'package:phoenix/features/pnl/bloc/pnl_bloc.dart';
import 'package:phoenix/features/pnl/data/pnl_data_provider.dart';
import 'package:phoenix/features/pnl_graph/bloc/pnl_graph_bloc.dart';
import 'package:phoenix/features/pnl_graph/provider/pnl_graph_provider.dart';
import 'package:phoenix/features/pnl_graph/repository/pnl_graph_repository.dart';
import 'package:phoenix/features/portfolio_data/bloc/portfolio_bloc.dart';
import 'package:phoenix/features/portfolio_data/data/provider/portfolio_provider.dart';
import 'package:phoenix/features/portfolio_data/data/repository/portfolio_repository.dart';
import 'package:phoenix/features/security_list/bloc/security_list_bloc.dart';
import 'package:phoenix/features/security_list/data/provider/security_provider.dart';
import 'package:phoenix/features/security_list/data/repository/security_repository.dart';
import 'package:phoenix/features/trades/bloc/trades_bloc.dart';
import 'package:phoenix/features/trades/data/trades_data_provider.dart';
import 'package:phoenix/features/watchlist/bloc/watchist_bloc.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/features/websocket/data/provider/web_socket_proto_provider.dart';
import 'package:phoenix/features/option_greeks_websocket/bloc/option_greeks_websocket_bloc.dart';
import 'package:phoenix/features/websocket/data/provider/option_greeks_websocket_provider.dart';
import 'package:phoenix/features/option_greeks_rest/bloc/option_greeks_rest_bloc.dart';
import 'package:phoenix/features/option_greeks_rest/data/provider/option_greeks_rest_provider.dart';
import 'package:phoenix/router_generator.dart';
import 'package:phoenix/screens/login/bio_metric_screen.dart';
import 'package:phoenix/features/watchlist/data/watchlist_provider.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/services/security_cache_service.dart';
import 'package:provider/provider.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_event.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';

import 'features/authentication/data/data_provider/biometric_service.dart';

class PhoenixApp extends StatefulWidget {
  const PhoenixApp({super.key});

  @override
  State<PhoenixApp> createState() => _PhoenixAppState();
}

class _PhoenixAppState extends State<PhoenixApp> {
  final BiometricService _biometricService = BiometricService();
  bool _isAuthenticated = false;

  Future<void> _checkBiometric() async {
    if (await _biometricService.isBiometricEnabled()) {
      final authenticated = await _biometricService.authenticate();
      setState(() {
        _isAuthenticated = authenticated;
      });

      if (!authenticated) {
        // If authentication fails or is canceled, exit the app
        SystemNavigator.pop();
      }
    } else {
      setState(() {
        _isAuthenticated = true;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _checkBiometric();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isAuthenticated) {
      return BioMetricScreen(checkBiometric: _checkBiometric);
    }

    return MultiRepositoryProvider(
      providers: [
        // Authentication Repository
        RepositoryProvider(
          create: (context) => AuthRepository(AuthService()),
        ),
        // Order Form Repository
        RepositoryProvider(
          create: (context) => OrderFormRepository(OrderFormProvider()),
        ),
        // Portfolio Repository
        RepositoryProvider(create: (context) => PortfolioProvider()),
        RepositoryProvider(
          create: (context) =>
              PortfolioRepository(context.read<PortfolioProvider>()),
        ),
        //PNL Repository
        RepositoryProvider(create: (context) => PnlDataProvider()),
        //Trades Repository
        RepositoryProvider(create: (context) => TradesDataProvider()),
        // Order State Repository
        RepositoryProvider(create: (context) => OrderStateProvider()),
        RepositoryProvider(
          create: (context) => OrderStateRepository(
              provider: context.read<OrderStateProvider>()),
        ),
        // Security Repository
        RepositoryProvider(create: (context) => SecurityProvider()),
        RepositoryProvider(
          create: (context) =>
              SecurityRepository(provider: context.read<SecurityProvider>()),
        ),
        //Commissions Repository
        RepositoryProvider(create: (context) => CommissionsProvider()),
        //Clients Data
        RepositoryProvider(create: (context) => ClientsProvider()),
        RepositoryProvider(
          create: (context) => ClientsDataRepo(context.read<ClientsProvider>()),
        ),
        //Margins Data
        RepositoryProvider(create: (context) => MarginProvider()),

        //LatestZenOrder
        RepositoryProvider(create: (context) => LatestZenOrderStateProvider()),

        //PNL Graph
        RepositoryProvider(
          create: (context) => PnlGraphProvider(),
        ),
        RepositoryProvider(
          create: (context) =>
              PnlGraphRepository(context.read<PnlGraphProvider>()),
        ),
      ],
      child: MultiBlocProvider(
        providers: [
          // Authentication Bloc
          BlocProvider(
            create: (context) => AuthBloc(context.read<AuthRepository>()),
          ),
          //Bottom Navigation BloC
          BlocProvider(
            create: (context) => BottomNavigationBloc(),
          ),
          // WebSocket Bloc - Will connect after authentication - for proto ticks streaming
          BlocProvider(
            create: (context) => WebSocketBloc(WebSocketProtoProvider()),
          ),
          // Option Greeks WebSocket Bloc - Will connect when option chain page is opened
          BlocProvider(
            create: (context) => OptionGreeksWebSocketBloc(OptionGreeksWebSocketProvider()),
          ),
          // Option Greeks REST Bloc - Fallback when WebSocket is down
          BlocProvider(
            create: (context) => OptionGreeksRestBloc(OptionGreeksRestProvider()),
          ),
          // Portfolio Bloc - Fetches positions on app start
          BlocProvider(
            create: (context) =>
                PortfolioBloc(context.read<PortfolioRepository>()),
          ),
          //PNL Bloc
          BlocProvider(
            create: (context) => PnlBloc(context.read<PnlDataProvider>()),
          ),
          //Trades Bloc
          BlocProvider(
            create: (context) => TradesBloc(context.read<TradesDataProvider>()),
          ),
          // Order Form Bloc
          BlocProvider(
            create: (context) =>
                OrdersBloc(context.read<OrderFormRepository>()),
          ),
          // Orders State Bloc
          BlocProvider(
            create: (context) =>
                OrdersStateBloc(context.read<OrderStateRepository>()),
          ),
          // Security List Bloc //fetch on app start
          BlocProvider(
            create: (context) =>
                SecurityListBloc(context.read<SecurityRepository>())
                  ..add(FetchSecurityListEvent()),
          ),
          //Commissions Data Map //fetch on app start
          BlocProvider(
            create: (context) =>
                CommissionsDataMapBloc(context.read<CommissionsProvider>())
                  ..add(FetchCommissionsData()),
                  lazy: false,
          ),
          // Clients data
          BlocProvider(
            create: (context) =>
                ClientsDataBloc(context.read<ClientsDataRepo>())
                  ..add(FetchClientsData()),
            lazy: false,
          ),
          // Margins Data
          BlocProvider(
            create: (context) => MarginBloc(context.read<MarginProvider>()),
          ),
          // Latest zen order state
          BlocProvider(
            create: (context) => LatestZenOrderStateBloc(
                context.read<LatestZenOrderStateProvider>()),
          ),
          //PNL Graph
          BlocProvider(
            create: (context) =>
                PnlGraphBloc(context.read<PnlGraphRepository>()),
          ),
          //Broker Data Map
          BlocProvider(
            create: (context) => BrokerDataMapBloc(),
          ),  
          // Watchlist Provider must come before WatchistBloc
          ChangeNotifierProvider(create: (_) => WatchlistProvider()),
          BlocProvider(
            create: (context) => WatchistBloc(context.read<WatchlistProvider>()),
          ),
          // Theme Bloc
          BlocProvider(
            create: (context) => ThemeBloc()..add(LoadTheme()),
          ),

        ],
        child: MultiBlocListener(
          listeners: [
            // Listen for authentication success and trigger WebSocket connection
            BlocListener<AuthBloc, AuthState>(
              listener: (context, state) {
                debugPrint("🔑 Auth state changed");
                if (state is AuthAuthenticated) {
                  final authToken =
                      state.credentialsModel.accessToken; // Extract auth token
                  context
                      .read<WebSocketBloc>()
                      .add(WebSocketConnect(authToken)); // Connect WebSocket

                  // when ever client changes the broker data will be laoded to a map
                  final brokers = state.credentialsModel.brokers;
                   
                   context.read<BrokerDataMapBloc>().add(LoadBrokerData(brokers));
                }
              },
            ),
            // Listen for security list loading and mark cache as updated
            BlocListener<SecurityListBloc, SecurityListState>(
              listener: (context, state) {
                if (state is SecurityListLoaded) {
                  debugPrint("📋 Security list loaded at app start, marking cache as updated");
                  SecurityCacheService.markCacheUpdated();
                }
              },
            ),
          ],

          child: BlocBuilder<ThemeBloc, ThemeState>(
            builder: (context, themeState) {
              return MaterialApp(
                theme: themeState.themeData,
                title: 'Phoenix',
                debugShowCheckedModeBanner: false,
                initialRoute: '/',
                onGenerateRoute: RouterGenerator.generateRoute,
                builder: (context, child) => child!,
              );
            },
          ),
        ),
      ),
    );
  }
}
