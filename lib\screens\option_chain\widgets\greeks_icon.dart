import 'package:flutter/material.dart';

class GreeksIcon extends StatelessWidget {
  const GreeksIcon({super.key, required this.name});
  final String name;

  @override
  Widget build(BuildContext context) {
    final greekData = _getGreekData(name.toLowerCase());

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: greekData.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ImageIcon(
        AssetImage(greekData.assetPath),
        color: greekData.color,
        size: 18,
      ),
    );
  }

  _GreekData _getGreekData(String name) {
    switch (name) {
      case 'theta':
        return _GreekData('images/greeks/theta_icon.png', Colors.orange);
      case 'gamma':
        return _GreekData('images/greeks/gamma_icon.png', Colors.blue);
      case 'delta':
        return _GreekData('images/greeks/delta_icon.png', Colors.yellow.shade700);
      case 'vega':
        return _GreekData('images/greeks/vega_icon.png', Colors.green);
      case 'rho':
        return _GreekData('images/greeks/rho_icon.png', Colors.purple);
      default:
        return _GreekData('images/greeks/default_icon.png', Colors.grey);
    }
  }
}

class _GreekData {
  final String assetPath;
  final Color color;

  _GreekData(this.assetPath, this.color);
}
