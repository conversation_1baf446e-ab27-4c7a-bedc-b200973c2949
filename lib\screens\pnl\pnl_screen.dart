import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/pnl/bloc/pnl_bloc.dart';
import 'package:phoenix/features/pnl/model/pnl_data_model.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/screens/pnl/pnl_list_builder.dart';
import 'package:phoenix/services/search_service.dart';
import 'package:phoenix/services/sort_service.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/circular_loader.dart';
import 'package:phoenix/widgets/pnl/pnl_bottom_bar.dart';
import 'package:phoenix/widgets/pnl/pnl_type_filter_bar.dart';
import 'package:phoenix/widgets/search/custom_search_bar.dart';
import 'package:phoenix/widgets/search_refresh_sort_bar/search_refresh_sort_bar.dart';
import 'package:phoenix/widgets/toast/custom_toast.dart';

class PnlScreen extends StatefulWidget {
  const PnlScreen({super.key});

  @override
  State<PnlScreen> createState() => _PnlScreenState();
}

class _PnlScreenState extends State<PnlScreen> {
  final SearchService _searchService = SearchService.instance;
  final SortService _sortService = SortService.instance;
  bool _showSearch = false;
  final TextEditingController _searchController = TextEditingController();
  SortOption? _currentSortOption;
  bool _isAscending = true;
  String selectedFilter = 'latest';

  //total
  //double totalRealized = 0;
  //double totalUnRealized = 0;

  void _toggleSearch() {
    setState(() {
      _showSearch = !_showSearch;
      if (!_showSearch) {
        _searchController.clear();
      }
    });
  }

  void _showSortOptions() {
    _sortService.showSortOptions(
      context: context,
      currentSortOption: _currentSortOption,
      isAscending: _isAscending,
      onSortChanged: (option, ascending) {
        setState(() {
          _currentSortOption = option;
          _isAscending = ascending;
        });
      },
      availableOptions: [SortOption.alphabetical,SortOption.date,SortOption.unrealized,SortOption.realized],
    );
  }

  List<PositionPnL> _sortPnlData(List<PositionPnL> pnlData) {
    return _sortService.sortList(
      items: pnlData,
      sortOption: _currentSortOption,
      isAscending: _isAscending,
      sortFunctions: {
        SortOption.alphabetical: (pnl) => pnl.tradingSymbol,
        SortOption.date: (pnl) => pnl.date,
        SortOption.unrealized: (pnl) => pnl.unRealized.getByKey(selectedFilter)!,
        SortOption.realized: (pnl) => pnl.realized.getByKey(selectedFilter)!,
      },
    );
  }

  @override
  void initState() {
    super.initState();
    debugPrint("🌙 PNL Screen init");

    _fetchPnlData();
    final state = context.read<PnlBloc>().state;
    debugPrint(state.toString());
    if (state is PnlLoaded) {
      debugPrint("PNL Data Loaded");
      debugPrint(state.pnlData.length.toString());
    }
  }

  bool _matchesSearchQuery(PositionPnL position, String query) {
    return _searchService.matchesSearchQuery<PositionPnL>(position, query);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _fetchPnlData() {
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthUnauthenticated) {
      debugPrint(authState.toString());
      Navigator.pushReplacementNamed(context, '/login');
    } else if (authState is AuthAuthenticated) {
      context.read<PnlBloc>().add(
            FetchPnlData(
              authState.credentialsModel.clientId,
            ),
          );
    } else {
      ZenSnackbar.show(
        context,
        type: ToastType.error,
        "Unauthenticated",
      );
    }
  }

  void _updateWebSocketSubscriptions(List<PositionPnL> data) {
    List<int> stockIds =
        data.map((d) => d.positionCompositeKey.zenSecId).toList();

    debugPrint(
      "🔄 Updating WebSocket subscriptions for pnl screen - ${stockIds.length} stocks",
    );
    context.read<WebSocketBloc>().add(WebSocketSelectMultipleStocks(stockIds));
  }

  @override
  Widget build(BuildContext context) {
    double totalRealized = 0;
    double totalUnRealized = 0;
    return Scaffold(
      backgroundColor: ThemeConstants.backgroundColor,
      body: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SearchRefreshSortBar(
                refresh: _fetchPnlData,
                toggleSearch: _toggleSearch,
                showSortOptions: _showSortOptions,
                isAscending: _isAscending,
                currentSortOption: _currentSortOption,
              ),
              if (_showSearch)
                CustomSearchBar(
                  controller: _searchController,
                  hintText: 'Search positions...',
                  autofocus: true,
                  onSearch: (query) {
                    setState(() {});
                  },
                  onClose: _toggleSearch,
                ),
              const SizedBox(height: 10),
              PnlTypeFilterBar(onFilterSelected: (filter) {
                setState(() {
                  selectedFilter = filter;
                });
                // Handle filter selection
              }),
              Expanded(
                child: BlocBuilder<PnlBloc, PnlState>(
                  builder: (context, state) {
                    if (state is PnlLoading) {
                      return const Center(
                        child: CircularLoader(),
                      );
                    } else if (state is PnlLoaded) {
                      _updateWebSocketSubscriptions(state.pnlData);
                      return PnlListBuilder(
                        data: _sortPnlData(_searchController.text.isEmpty
                            ? state.pnlData
                            : state.pnlData
                                .where((position) => _matchesSearchQuery(
                                    position, _searchController.text))
                                .toList()),
                        emptyMessage: "No Pnl to show",
                        refresh: _fetchPnlData,
                        filter: selectedFilter,
                      );
                    } else if (state is PnlError) {
                      return Center(
                        child: Text(
                          state.error,
                          style: const TextStyle(color: Colors.red),
                        ),
                      );
                    } else {
                      return const Center(
                        child: Text('Something went wrong.'),
                      );
                    }
                  },
                ),
              ),
             
            ],
          )
        ],
      ),
      bottomSheet: BlocBuilder<PnlBloc, PnlState>(
        builder: (context, state) {
          if (state is PnlLoading) {
            return const SizedBox.shrink();
          } else if (state is PnlLoaded) {
            // Calculate total realized and unrealized PnL
            debugPrint("👺 PNL bottom calulations");
            var filteredData = _sortPnlData(_searchController.text.isEmpty
                ? state.pnlData
                : state.pnlData
                    .where((position) =>
                        _matchesSearchQuery(position, _searchController.text))
                    .toList());

            totalUnRealized = 0;
            totalRealized = 0;

            for (var data in filteredData) {
              if(selectedFilter=='latest'){
                totalRealized += data.realized.latest;
                totalUnRealized += data.unRealized.latest;
              } else {
                //debugPrint("${data.realized.latest - data.realized.getByKey(selectedFilter)!}");
                totalRealized += (data.realized.latest - data.realized.getByKey(selectedFilter)!);
                //debugPrint("${data.unRealized.latest - data.unRealized.getByKey(selectedFilter)!}");
                totalUnRealized += (data.unRealized.latest - data.unRealized.getByKey(selectedFilter)!);
              }
            }
          } else if (state is PnlError) {
            return const SizedBox.shrink();
          }

          return PnlBottomBar(
              invested: 0,
              current: 0,
              daysPnL: totalRealized,
              daysPnLPercentage: totalUnRealized,
              changeAmount: 0,
              changePercentage: 0);
        },
      ),
    );
  }
}
