import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:meta/meta.dart';
import 'package:phoenix/features/margin_state/data/provider/margin_provider.dart';

part 'margin_state_event.dart';
part 'margin_state_state.dart';

class MarginBloc extends Bloc<MarginEvent, MarginState> {
  final MarginProvider _marginProvider;

  MarginBloc(this._marginProvider) : super(MarginInitial()) {
    on<MarginFetchEvent>(_onMarginFetchEvent);
  }
  void _onMarginFetchEvent(
      MarginFetchEvent event, Emitter<MarginState> emit) async {
    debugPrint("reach __onMarginFetchEvent");
    emit(MarginLoading());
    try {
      final marginData = await _marginProvider.fetchMarginData(
          event.clientId, event.accountId);

      emit(MarginLoaded(margin: marginData.availableCash));
    } catch (e) {
      emit(MarginError(error: e.toString()));
    }
  }
}
