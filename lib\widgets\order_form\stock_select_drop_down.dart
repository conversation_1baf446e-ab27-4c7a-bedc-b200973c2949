import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/security_list/bloc/security_list_bloc.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';

class StockSelectDropDown extends StatefulWidget {
  final String? selectedValue;
  final void Function(Map<String, dynamic>) action;
  final TextEditingController textEditingController;

  const StockSelectDropDown({
    super.key,
    required this.selectedValue,
    required this.action,
    required this.textEditingController,
  });

  @override
  _StockSelectDropDownState createState() => _StockSelectDropDownState();
}

class _StockSelectDropDownState extends State<StockSelectDropDown> {
  List<SecurityModel> fullList = []; // Full list of securities
  List<SecurityModel> displayList = []; // Filtered list (max 20 items)

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SecurityListBloc, SecurityListState>(
      builder: (context, state) {
        if (state is SecurityListLoading) {
          return const Text("Loading...");
        } else if (state is SecurityListLoaded) {
          fullList = state.equityList;
          displayList = fullList.take(20).toList(); // Initially show 20 items

          return Container(
            padding: const EdgeInsets.all(2),
            alignment: Alignment.center,
            height: 40,
            width: 205,
            decoration: BoxDecoration(
              color: const Color(0xff353535),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Center(
              child: DropdownButtonHideUnderline(
                child: DropdownButton2<String>(
                  isExpanded: true,
                  items: displayList
                      .map(
                        (SecurityModel item) => DropdownMenuItem<String>(
                          value: item.tradingSymbol, // Use the trading symbol
                          child: Text(
                            item.tradingSymbol,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Color(0xffADADAD),
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      )
                      .toList(),
                  value: widget.selectedValue,
                  onChanged: (value) {
                    // Find selected item based on trading symbol
                    final selectedItem = fullList.firstWhere(
                      (item) => item.tradingSymbol == value,
                    );

                    // Pass selected item (zenId & lotSize) to the action callback
                    widget.action({
                      'zenId': selectedItem.zenId,
                      'lotSize': selectedItem.lotSize,
                    });
                  },
                  buttonStyleData: const ButtonStyleData(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    height: 40,
                    width: 200,
                  ),
                  dropdownStyleData: DropdownStyleData(
                    decoration: BoxDecoration(
                      color: const Color(0xff353535),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    maxHeight: 300,
                  ),
                  menuItemStyleData: const MenuItemStyleData(
                    height: 40,
                  ),
                  dropdownSearchData: DropdownSearchData(
                    searchController: widget.textEditingController,
                    searchInnerWidgetHeight: 200,
                    searchInnerWidget: StatefulBuilder(
                      builder: (context, setStateInner) {
                        return Container(
                          height: 50,
                          padding: const EdgeInsets.all(8),
                          child: TextFormField(
                            style: const TextStyle(
                                color: Colors.white, fontSize: 16),
                            expands: true,
                            maxLines: null,
                            controller: widget.textEditingController,
                            decoration: InputDecoration(
                              isDense: true,
                              hintText: 'Search Stock',
                              hintStyle: const TextStyle(
                                fontSize: 19,
                                color: Color(0xffADADAD),
                                overflow: TextOverflow.ellipsis,
                                fontWeight: FontWeight.w400,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            onChanged: (query) {
                              setStateInner(() {
                                _filterList(query);
                              });
                            },
                          ),
                        );
                      },
                    ),
                  ),
                  onMenuStateChange: (isOpen) {
                    if (!isOpen) {
                      widget.textEditingController.clear();
                      setState(() {
                        displayList = fullList.take(20).toList();
                      });
                    }
                  },
                ),
              ),
            ),
          );
        } else if (state is SecurityListError) {
          return ThemeConstants.zenText("Error",false);
        } else {
          return ThemeConstants.zenText("Something went wrong",false);
        }
      },
    );
  }

  void _filterList(String query) {
    print(query);
    if (query.isEmpty) {
      displayList = fullList.take(20).toList(); // Show first 20 if no search
    } else {
       print(query);
      displayList = fullList
          .where((item) =>
              item.tradingSymbol.toLowerCase().contains(query.toLowerCase()))
          .take(20)
          .toList(); 
          print(displayList);// Show only top 20 matches
      
    }
  }
}
