import 'package:flutter/material.dart' hide BoxDecoration, BoxShadow;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/features/portfolio_data/bloc/portfolio_bloc.dart';
import 'package:phoenix/screens/portfolio/portfolio_list_builder.dart';
import 'package:phoenix/widgets/circular_loader.dart';
import 'package:phoenix/widgets/empty_state/empty_container.dart';
import 'package:phoenix/widgets/portfolio/portfolio_net_worth_bar.dart';
import 'package:phoenix/widgets/search_refresh_sort_bar/search_refresh_sort_bar.dart';
import 'package:phoenix/widgets/tab_bar/app_tab_bar2.dart';
import '../../features/portfolio_data/model/position_model.dart';
import '../../services/search_service.dart';
import '../../services/sort_service.dart';
import '../../widgets/search/custom_search_bar.dart';
import 'package:phoenix/features/pnl/bloc/pnl_bloc.dart';
import 'package:phoenix/screens/pnl/pnl_list_builder.dart';
import 'package:phoenix/widgets/pnl/pnl_type_filter_bar.dart';
import 'package:phoenix/widgets/pnl/pnl_bottom_bar.dart';
import 'package:phoenix/features/pnl/model/pnl_data_model.dart';

/// Portfolio Screen
/// This screen displays the user's portfolio data.
/// We use the PortfolioBloc to fetch and manage the portfolio data.
/// Here we subscribe to websocketbloc to get price updates for the stocks availabe in the position on every refresh
class PortfolioScreen extends StatefulWidget {
  const PortfolioScreen({super.key});

  @override
  State<PortfolioScreen> createState() => _PortfolioScreenState();
}

class _PortfolioScreenState extends State<PortfolioScreen>
    with TickerProviderStateMixin {
  late final AnimationController _formSheetAnimeController;
  late final TabController _tabController;
  final SearchService _searchService = SearchService.instance;
  final SortService _sortService = SortService.instance;
  bool showOpenPositions = true;

  // Add these variables
  bool _showSearch = false;
  final TextEditingController _searchController = TextEditingController();
  SortOption? _currentSortOption;
  bool _isAscending = true;

  // PnL tab state
  final SearchService _pnlSearchService = SearchService.instance;
  final SortService _pnlSortService = SortService.instance;
  bool _pnlShowSearch = false;
  final TextEditingController _pnlSearchController = TextEditingController();
  SortOption? _pnlCurrentSortOption;
  bool _pnlIsAscending = true;
  String _selectedPnlFilter = 'latest';

  void _toggleSearch() {
    setState(() {
      _showSearch = !_showSearch;
      if (!_showSearch) {
        _searchController.clear();
      }
    });
  }

  void _showSortOptions() {
    _sortService.showSortOptions(
      context: context,
      currentSortOption: _currentSortOption,
      isAscending: _isAscending,
      onSortChanged: (option, ascending) {
        setState(() {
          _currentSortOption = option;
          _isAscending = ascending;
        });
      },
      availableOptions: [
        SortOption.alphabetical,
        SortOption.invested,
        SortOption.lastTradedPrice,
        SortOption.date,
        SortOption.percentChange
      ],
    );
  }

  List<PositionsModel> _sortPositions(List<PositionsModel> positions) {
    return _sortService.sortList(
      items: positions,
      sortOption: _currentSortOption,
      isAscending: _isAscending,
      sortFunctions: {
        SortOption.alphabetical: (position) => position.tradingSymbol,
        SortOption.invested: (position) => position.openCost,
        SortOption.lastTradedPrice: (position) => position.latestPrice,
        SortOption.date: (position) => position.date,
        SortOption.percentChange: (position) =>
            position.unrealizedPnlPercentageChange,
      },
    );
  }

  bool _matchesSearchQuery(PositionsModel position, String query) {
    return _searchService.matchesSearchQuery<PositionsModel>(position, query);
  }

  List<PositionPnL> _sortPnlData(List<PositionPnL> pnlData) {
    return _pnlSortService.sortList(
      items: pnlData,
      sortOption: _pnlCurrentSortOption,
      isAscending: _pnlIsAscending,
      sortFunctions: {
        SortOption.alphabetical: (pnl) => pnl.tradingSymbol,
        SortOption.date: (pnl) => pnl.date,
        SortOption.unrealized: (pnl) =>
            pnl.unRealized.getByKey(_selectedPnlFilter)!,
        SortOption.realized: (pnl) =>
            pnl.realized.getByKey(_selectedPnlFilter)!,
      },
    );
  }

  bool _matchesPnlSearchQuery(PositionPnL position, String query) {
    return _pnlSearchService.matchesSearchQuery<PositionPnL>(position, query);
  }

  @override
  void initState() {
    super.initState();
    debugPrint("👺 Portfolio Screen init");

    _formSheetAnimeController = BottomSheet.createAnimationController(this);
    _formSheetAnimeController.duration = Duration(milliseconds: 850);

    _tabController = TabController(length: 3, vsync: this);
    // _tabController.addListener(() {
    //   if (_tabController.index == 2 &&
    //       _tabController.indexIsChanging == false) {
    //     // PnL tab selected
    //     final authState = context.read<AuthBloc>().state;
    //     if (authState is AuthAuthenticated) {
    //       debugPrint(
    //           '[PnL Tab] TabController triggered fetch for clientId: \\${authState.credentialsModel.clientId}');
    //       context
    //           .read<PnlBloc>()
    //           .add(FetchPnlData(authState.credentialsModel.clientId));
    //     }
    //   }
    // });

    final authState = context.read<AuthBloc>().state;

    if (authState is AuthUnauthenticated) {
      debugPrint(authState.toString());
      Navigator.pushReplacementNamed(context, '/login');
    }

    if (authState is AuthAuthenticated) {
      context
          .read<PortfolioBloc>()
          .add(FetchPortfolio(authState.credentialsModel.clientId));
      context.read<PnlBloc>().add(
            FetchPnlData(
              authState.credentialsModel.clientId,
            ),
          );
    } else {
      // Handle unauthenticated case (e.g., show login dialog)
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("You need to log in first")),
      );
    }

    // To initialize which screen to show
    _positionsScreenInitializer();
  }

  void _positionsScreenInitializer() {
    // To initialize which screen to show
    final portfolioState = context.read<PortfolioBloc>().state;
    if (portfolioState is PortfolioLoaded) {
      if (portfolioState.openPositions.isEmpty) {
        setState(() {
          showOpenPositions = false;
        });
        debugPrint("showOpenPositions = false");
      }
    }
  }

  void _refreshPositionsData() {
    //to refresh the positions data
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      context.read<PortfolioBloc>().add(
            FetchPortfolio(
              authState.credentialsModel.clientId,
            ),
          );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("You need to log in first")),
      );
    }
  }

  void _updateWebSocketSubscriptionsForPortFolio(
      List<PositionsModel> holdings) {
    List<int> stockIds = holdings
        .map((holding) => holding.positionCompositeKey.zenSecId)
        .toList();

    debugPrint(
        "🔄 Updating WebSocket subscriptions for portfolio list ${stockIds.length} stocks");
    context.read<WebSocketBloc>().add(WebSocketSelectMultipleStocks(stockIds));
  }

  @override
  void dispose() {
    _tabController.dispose();
    // Dispose of PortfolioDataUpdateService
    // _portfolioDataUpdateService.dispose();
    debugPrint("Portfolio Screen socket dispose");

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return DefaultTabController(
          length: 3, // Now three tabs: Open, Closed, PnL
          child: Scaffold(
            backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            BlocBuilder<PortfolioBloc, PortfolioState>(
              builder: (context, portfolioState) {
                int openCount = 0;
                int closedCount = 0;

                if (portfolioState is PortfolioLoaded) {
                  openCount = portfolioState.openPositions.length;
                  closedCount = portfolioState.closedPositions.length;
                }

                return BlocBuilder<PnlBloc, PnlState>(
                  builder: (context, pnlState) {
                    int pnlCount = 0;
                    if (pnlState is PnlLoaded) {
                      pnlCount = pnlState.pnlData.length;
                    }

                    return AppTabBar2(
                      count1: openCount,
                      count2: closedCount,
                      count3:
                          pnlCount, // PnL count, set to 0 or actual count if available
                      title1: "Open",
                      title2: "Closed",
                      title3: "PnL",
                      controller: _tabController,
                    );
                  },
                );
              },
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // Open positions tab
                  BlocListener<PortfolioBloc, PortfolioState>(
                    listener: (context, portfolioState) {
                      if (portfolioState is PortfolioLoaded) {
                        List<PositionsModel> positions = [
                          ...portfolioState.openPositions,
                          ...portfolioState.closedPositions
                        ];
                        _updateWebSocketSubscriptionsForPortFolio(positions);
                      }
                    },
                    child: BlocBuilder<PortfolioBloc, PortfolioState>(
                      builder: (context, portfolioState) {
                        if (portfolioState is PortfolioLoading) {
                          return const Center(child: CircularLoader());
                        } else if (portfolioState is PortfolioLoaded) {
                          return BlocBuilder<WebSocketBloc, WebSocketState>(
                            builder: (context, webSocketState) {
                              Map<int, double> stockPrices = {};
                              //Portfolio Net Worth bar Calculations are below
                              if (webSocketState
                                  is WebSocketMultipleStockPricesUpdated) {
                                stockPrices = webSocketState.stockPrices;
                                debugPrint(
                                  "📊 Received price updates for ${stockPrices.length} stocks",
                                );
                              }

                              if (portfolioState.openPositions.isEmpty &&
                                  portfolioState.closedPositions.isEmpty) {
                                return EmptyContainer(
                                  title: "No Holdings to show",
                                  message:
                                      "You can place an order from the orders page.",
                                  imagePath: "images/database-positions-no.png",
                                );
                              }

                              //filtering to reflect the total based on filtering
                              var filteredData = _sortPositions(
                                  _searchController.text.isEmpty
                                      ? portfolioState.openPositions
                                      : portfolioState.openPositions
                                          .where((position) =>
                                              _matchesSearchQuery(position,
                                                  _searchController.text))
                                          .toList());

                              double invested = 0;
                              double current = 0;
                              double daysPnL = 0;
                              double calculatedInvested = 0;

                              for (var holding in filteredData) {
                                int stockId =
                                    holding.positionCompositeKey.zenSecId;
                                invested += holding.openCost;

                                calculatedInvested +=
                                    holding.averageCostPerShare *
                                        holding.position;

                                // Use real-time price if available, otherwise use holding's latest price
                                double? livePrice = stockPrices[stockId];
                                double fallbackPrice = holding
                                    .latestPrice; //i.e price from api data
                                double currentPrice =
                                    (livePrice != null && livePrice > 0)
                                        ? livePrice
                                        : fallbackPrice;

                                //debugPrint("$livePrice $fallbackPrice");
                                // double currentPrice = stockPrices[stockId] ?? holding.latestPrice;
                                current += currentPrice * holding.position;
                                //double currentUnrealized = (currentPrice * holding.position) - holding.openCost;
                                //double yesterdayLTD = (holding.sodPrice * holding.position) - holding.openCost;
                                //daysPnL += currentUnrealized - yesterdayLTD;
                                double prevClose = holding.sodPrice;
                                daysPnL += (currentPrice - prevClose) *
                                    holding.position;
                              }

                              /// in below code I have replaced invested with calculatedInvested

                              double daysPnLPercentage = calculatedInvested != 0
                                  ? (daysPnL / calculatedInvested) * 100
                                  : 0;
                              double changeAmount =
                                  current - calculatedInvested;
                              double changePercentage = calculatedInvested != 0
                                  ? (changeAmount / calculatedInvested) * 100
                                  : 0;
                              //debugPrint("💰 Portfolio values - Current: $current, Invested: $invested, P&L: $daysPnL, $daysPnLPercentage");
                              return Column(
                                children: [
                                  SearchRefreshSortBar(
                                    refresh: () {
                                      _refreshPositionsData();
                                      _positionsScreenInitializer();
                                    },
                                    toggleSearch: _toggleSearch,
                                    showSortOptions: _showSortOptions,
                                    isAscending: _isAscending,
                                    currentSortOption: _currentSortOption,
                                  ),
                                  if (_showSearch)
                                    CustomSearchBar(
                                      controller: _searchController,
                                      hintText: 'Search positions...',
                                      autofocus: true,
                                      onSearch: (query) {
                                        setState(() {});
                                      },
                                      onClose: _toggleSearch,
                                    ),
                                  Expanded(
                                      child: PortfolioListBuilder(
                                    data: _sortPositions(
                                      _searchController.text.isEmpty
                                          ? portfolioState.openPositions
                                          : portfolioState.openPositions
                                              .where((position) =>
                                                  _matchesSearchQuery(position,
                                                      _searchController.text))
                                              .toList(),
                                    ),
                                    emptyMessage: "No Holdings to show",
                                    formSheetAnimeController:
                                        _formSheetAnimeController,
                                  )),
                                  PortfolioNetWorthBar(
                                      invested: invested,
                                      current: current,
                                      daysPnL: daysPnL,
                                      daysPnLPercentage: daysPnLPercentage,
                                      changeAmount: changeAmount,
                                      changePercentage: changePercentage)
                                ],
                              );
                            },
                          );
                        } else if (portfolioState is PortfolioError) {
                          return Center(
                            child: Text(
                              portfolioState.error,
                              style: const TextStyle(
                                  color: ThemeConstants.titleRedColor,
                                  fontSize: 14),
                            ),
                          );
                        } else {
                          return const Center(
                            child: Text('Something went wrong.'),
                          );
                        }
                      },
                    ),
                  ),

                  // Closed positions tab
                  BlocBuilder<PortfolioBloc, PortfolioState>(
                      builder: (context, portfolioState) {
                    if (portfolioState is PortfolioLoading) {
                      return const Center(child: CircularLoader());
                    } else if (portfolioState is PortfolioLoaded) {
                      return BlocBuilder<WebSocketBloc, WebSocketState>(
                        builder: (context, webSocketState) {
                          Map<int, double> stockPrices = {};
                          //Portfolio Net Worth bar Calculations are below
                          if (webSocketState
                              is WebSocketMultipleStockPricesUpdated) {
                            stockPrices = webSocketState.stockPrices;
                            debugPrint(
                              "📊 Received price updates for ${stockPrices.length} stocks",
                            );
                          }

                          if (portfolioState.closedPositions.isEmpty) {
                            return EmptyContainer(
                              title: "No Closed Positions to show",
                              message:
                                  "Try closing an open position to see it here.",
                              imagePath: "images/database-positions-no.png",
                            );
                          }

                          return Column(
                            children: [
                              SearchRefreshSortBar(
                                refresh: () {
                                  _refreshPositionsData();
                                  _positionsScreenInitializer();
                                },
                                toggleSearch: _toggleSearch,
                                showSortOptions: _showSortOptions,
                                isAscending: _isAscending,
                                currentSortOption: _currentSortOption,
                              ),
                              if (_showSearch)
                                CustomSearchBar(
                                  controller: _searchController,
                                  hintText: 'Search positions...',
                                  autofocus: true,
                                  onSearch: (query) {
                                    setState(() {});
                                  },
                                  onClose: _toggleSearch,
                                ),
                              Expanded(
                                  child: AnimatedOpacity(
                                opacity: 0.6,
                                duration: const Duration(milliseconds: 300),
                                child: PortfolioListBuilder(
                                  data: _sortPositions(
                                    _searchController.text.isEmpty
                                        ? portfolioState.closedPositions
                                        : portfolioState.closedPositions
                                            .where((position) =>
                                                _matchesSearchQuery(position,
                                                    _searchController.text))
                                            .toList(),
                                  ),
                                  emptyMessage:
                                      "Try closing an open position to see it here.",
                                  formSheetAnimeController:
                                      _formSheetAnimeController,
                                ),
                              )),
                            ],
                          );
                        },
                      );
                    } else if (portfolioState is PortfolioError) {
                      return Center(
                        child: Text(
                          portfolioState.error,
                          style: const TextStyle(
                              color: ThemeConstants.titleRedColor,
                              fontSize: 14),
                        ),
                      );
                    } else {
                      return const Center(
                        child: Text('Something went wrong.'),
                      );
                    }
                  }),
                  // PnL tab
                  BlocBuilder<PnlBloc, PnlState>(
                    builder: (context, pnlState) {
                      if (pnlState is PnlLoading) {
                        debugPrint('[PnL Tab] State: Loading');
                        return const Center(child: CircularLoader());
                      } else if (pnlState is PnlLoaded) {
                        debugPrint(
                            '[PnL Tab] Loaded data length: \\${pnlState.pnlData.length}');
                        // Filtering, sorting, and searching logic
                        List<PositionPnL> filteredData = _sortPnlData(
                          _pnlSearchController.text.isEmpty
                              ? pnlState.pnlData
                              : pnlState.pnlData
                                  .where((position) => _matchesPnlSearchQuery(
                                      position, _pnlSearchController.text))
                                  .toList(),
                        );
                        double totalUnRealized = 0;
                        double totalRealized = 0;
                        for (var data in filteredData) {
                          if (_selectedPnlFilter == 'latest') {
                            totalRealized += data.realized.latest;
                            totalUnRealized += data.unRealized.latest;
                          } else {
                            totalRealized += (data.realized.latest -
                                data.realized.getByKey(_selectedPnlFilter)!);
                            totalUnRealized += (data.unRealized.latest -
                                data.unRealized.getByKey(_selectedPnlFilter)!);
                          }
                        }
                        return Column(
                          children: [
                            SearchRefreshSortBar(
                              refresh: () {
                                final authState =
                                    context.read<AuthBloc>().state;
                                if (authState is AuthAuthenticated) {
                                  context.read<PnlBloc>().add(FetchPnlData(
                                      authState.credentialsModel.clientId));
                                }
                              },
                              toggleSearch: () {
                                setState(() {
                                  _pnlShowSearch = !_pnlShowSearch;
                                  if (!_pnlShowSearch) {
                                    _pnlSearchController.clear();
                                  }
                                });
                              },
                              showSortOptions: () {
                                _pnlSortService.showSortOptions(
                                  context: context,
                                  currentSortOption: _pnlCurrentSortOption,
                                  isAscending: _pnlIsAscending,
                                  onSortChanged: (option, ascending) {
                                    setState(() {
                                      _pnlCurrentSortOption = option;
                                      _pnlIsAscending = ascending;
                                    });
                                  },
                                  availableOptions: [
                                    SortOption.alphabetical,
                                    SortOption.date,
                                    SortOption.unrealized,
                                    SortOption.realized
                                  ],
                                );
                              },
                              isAscending: _pnlIsAscending,
                              currentSortOption: _pnlCurrentSortOption,
                            ),
                            if (_pnlShowSearch)
                              CustomSearchBar(
                                controller: _pnlSearchController,
                                hintText: 'Search positions...',
                                autofocus: true,
                                onSearch: (query) {
                                  setState(() {});
                                },
                                onClose: () {
                                  setState(() {
                                    _pnlShowSearch = false;
                                    _pnlSearchController.clear();
                                  });
                                },
                              ),
                            const SizedBox(height: 10),
                            PnlTypeFilterBar(
                              onFilterSelected: (filter) {
                                setState(() {
                                  _selectedPnlFilter = filter;
                                });
                              },
                            ),
                            Expanded(
                              child: PnlListBuilder(
                                data: filteredData,
                                emptyMessage: "No PnL to show",
                                refresh: () {
                                  final authState =
                                      context.read<AuthBloc>().state;
                                  if (authState is AuthAuthenticated) {
                                    context.read<PnlBloc>().add(FetchPnlData(
                                        authState.credentialsModel.clientId));
                                  }
                                },
                                filter: _selectedPnlFilter,
                              ),
                            ),
                            PnlBottomBar(
                              invested: 0,
                              current: 0,
                              daysPnL: totalRealized,
                              daysPnLPercentage: totalUnRealized,
                              changeAmount: 0,
                              changePercentage: 0,
                            ),
                          ],
                        );
                      } else if (pnlState is PnlError) {
                        debugPrint('[PnL Tab] Error: \\${pnlState.error}');
                        return Center(
                          child: Text(
                            pnlState.error,
                            style: const TextStyle(color: Colors.red),
                          ),
                        );
                      } else {
                        debugPrint('[PnL Tab] State: Unknown');
                        return const Center(
                          child: Text('Something went wrong.'),
                        );
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
        ));
      },
    );
  }
}
